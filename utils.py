"""
Utility functions for the Discord bot
Consolidates common functionality used across multiple modules
"""

import os
import json
import logging
import threading
import time
from typing import Dict, Any, Optional
from datetime import datetime

# Global file locks for thread-safe operations
_file_locks: Dict[str, threading.Lock] = {}

def get_file_lock(file_path: str) -> threading.Lock:
    """Get or create a lock for a specific file"""
    if file_path not in _file_locks:
        _file_locks[file_path] = threading.Lock()
    return _file_locks[file_path]

def load_json(file_path: str) -> Dict[str, Any]:
    """Thread-safe JSON loading with error handling"""
    with get_file_lock(file_path):
        try:
            if not os.path.exists(file_path):
                with open(file_path, 'w') as f:
                    json.dump({}, f)
                return {}

            with open(file_path, 'r') as f:
                content = f.read()
                if not content:
                    return {}
                return json.loads(content)

        except json.JSONDecodeError as e:
            logging.error(f"JSON decode error in {file_path}: {e}")
            return {}
        except IOError as e:
            logging.error(f"IO error reading {file_path}: {e}")
            return {}
        except Exception as e:
            logging.error(f"Error loading JSON file {file_path}: {e}")
            return {}

def save_json(file_path: str, data: Dict[str, Any]) -> bool:
    """Thread-safe JSON saving with atomic write"""
    with get_file_lock(file_path):
        try:
            # Create temp file for atomic write
            temp_path = f"{file_path}.tmp"
            with open(temp_path, 'w') as f:
                json.dump(data, f, indent=4)

            # Atomic rename
            os.replace(temp_path, file_path)
            return True
        except Exception as e:
            logging.error(f"Error saving JSON file {file_path}: {e}")
            return False

def ensure_file_exists(file_path: str, default_content: Any = None) -> bool:
    """Ensure a file exists, creating it with default content if needed"""
    try:
        if not os.path.exists(file_path):
            if default_content is None:
                default_content = {}
            
            with open(file_path, 'w') as f:
                if isinstance(default_content, (dict, list)):
                    json.dump(default_content, f, indent=4)
                else:
                    f.write(str(default_content))
            
            logging.info(f"Created file {file_path}")
            return True
        return True
    except Exception as e:
        logging.error(f"Error creating file {file_path}: {e}")
        return False

def get_script_directory() -> str:
    """Get the directory of the current script"""
    return os.path.dirname(os.path.abspath(__file__))

def setup_logging(log_file: str = 'bot.log', level: int = logging.INFO, console_level: int = logging.WARNING) -> None:
    """Setup dual logging configuration - detailed file logging + clean console output"""

    # Clear any existing handlers
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%H:%M:%S'
    )

    # File handler - comprehensive logging
    file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    file_handler.setLevel(level)
    file_handler.setFormatter(file_formatter)

    # Console handler - clean, essential output only
    console_handler = logging.StreamHandler()
    console_handler.setLevel(console_level)
    console_handler.setFormatter(console_formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # Suppress verbose third-party loggers in console
    logging.getLogger('discord').setLevel(logging.WARNING)
    logging.getLogger('discord.http').setLevel(logging.WARNING)
    logging.getLogger('discord.gateway').setLevel(logging.WARNING)
    logging.getLogger('discord.client').setLevel(logging.WARNING)
    logging.getLogger('motor').setLevel(logging.WARNING)
    logging.getLogger('pymongo').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)

def console_log(message: str, level: str = "INFO") -> None:
    """Log message to console with clean formatting"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if level == "ERROR":
        print(f"{timestamp} [ERROR] {message}")
    elif level == "WARNING":
        print(f"{timestamp} [WARN] {message}")
    elif level == "SUCCESS":
        print(f"{timestamp} [OK] {message}")
    elif level == "COMMAND":
        print(f"{timestamp} [CMD] {message}")
    else:
        print(f"{timestamp} [INFO] {message}")

def log_command_execution(interaction, command_name: str, success: bool = True) -> None:
    """Log command execution with clean console output and detailed file logging"""
    user = interaction.user
    guild = interaction.guild

    # Detailed file logging
    logging.info(f"Command executed: {command_name}")
    logging.info(f"User: {user.name}#{user.discriminator} (ID: {user.id})")
    logging.info(f"Guild: {guild.name} (ID: {guild.id}) if guild else 'DM'")
    logging.info(f"Success: {success}")

    # Clean console output
    guild_name = guild.name if guild else "DM"
    status = "✓" if success else "✗"
    console_log(f"{status} {command_name} | {user.name} | {guild_name}", "COMMAND")

def log_error_to_console(error: Exception, context: str = "") -> None:
    """Log error to console with clean formatting"""
    error_msg = f"{context}: {str(error)}" if context else str(error)
    console_log(error_msg, "ERROR")

    # Full traceback to file only
    logging.error(f"Error in {context}: {error}", exc_info=True)

def log_bot_status(message: str, status_type: str = "INFO") -> None:
    """Log bot status changes with clean console output"""
    console_log(message, status_type)
    logging.info(f"Bot Status: {message}")

def log_permission_check(interaction, command_name: str) -> bool:
    """Log permission check details for commands"""
    user = interaction.user
    guild = interaction.guild
    has_admin = user.guild_permissions.administrator
    logging.info(f"Permission Check - Command: {command_name}")
    logging.info(f"User: {user.name}#{user.discriminator} (ID: {user.id})")
    logging.info(f"Guild: {guild.name} (ID: {guild.id})")
    logging.info(f"Has Admin Permission: {has_admin}")
    return has_admin

def format_timestamp(timestamp: Optional[datetime] = None) -> str:
    """Format timestamp for logging and display"""
    if timestamp is None:
        timestamp = datetime.now()
    return timestamp.strftime("%Y-%m-%d %H:%M:%S")

def safe_int_convert(value: Any, default: int = 0) -> int:
    """Safely convert value to integer with default fallback"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_float_convert(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float with default fallback"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate string to max length with suffix"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def validate_discord_id(discord_id: Any) -> Optional[int]:
    """Validate and convert Discord ID to integer"""
    try:
        id_int = int(discord_id)
        # Discord IDs are typically 17-19 digits
        if 10**16 <= id_int <= 10**19:
            return id_int
        return None
    except (ValueError, TypeError):
        return None

def create_backup_filename(base_name: str, extension: str = ".json") -> str:
    """Create a backup filename with timestamp"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    name_without_ext = os.path.splitext(base_name)[0]
    return f"{name_without_ext}_backup_{timestamp}{extension}"

def cleanup_old_files(directory: str, pattern: str, max_age_days: int = 7) -> int:
    """Clean up old files matching pattern older than max_age_days"""
    import glob
    
    count = 0
    try:
        files = glob.glob(os.path.join(directory, pattern))
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        
        for file_path in files:
            if os.path.getmtime(file_path) < cutoff_time:
                os.remove(file_path)
                count += 1
                logging.info(f"Cleaned up old file: {file_path}")
    except Exception as e:
        logging.error(f"Error cleaning up files: {e}")
    
    return count

def get_file_size_mb(file_path: str) -> float:
    """Get file size in megabytes"""
    try:
        return os.path.getsize(file_path) / (1024 * 1024)
    except OSError:
        return 0.0

def ensure_directory_exists(directory: str) -> bool:
    """Ensure directory exists, creating it if needed"""
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Error creating directory {directory}: {e}")
        return False

class RateLimitTracker:
    """Simple rate limit tracking utility"""
    
    def __init__(self):
        self.requests = {}
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    def is_rate_limited(self, key: str, max_requests: int, window_seconds: int) -> bool:
        """Check if key is rate limited"""
        current_time = time.time()
        
        # Cleanup old entries periodically
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_entries(current_time - window_seconds)
            self.last_cleanup = current_time
        
        # Get or create request list for key
        if key not in self.requests:
            self.requests[key] = []
        
        # Remove old requests outside window
        cutoff_time = current_time - window_seconds
        self.requests[key] = [req_time for req_time in self.requests[key] if req_time > cutoff_time]
        
        # Check if rate limited
        if len(self.requests[key]) >= max_requests:
            return True
        
        # Add current request
        self.requests[key].append(current_time)
        return False
    
    def _cleanup_old_entries(self, cutoff_time: float):
        """Remove old entries from all keys"""
        for key in list(self.requests.keys()):
            self.requests[key] = [req_time for req_time in self.requests[key] if req_time > cutoff_time]
            if not self.requests[key]:
                del self.requests[key]

# Global rate limit tracker instance
rate_limit_tracker = RateLimitTracker()

async def safe_add_role(member, role, reason: str = None) -> bool:
    """Safely add a role to a member with proper error handling"""
    try:
        if not role:
            logging.warning(f"Cannot add role: Role is None for member {member.name}")
            return False

        if not member:
            logging.warning(f"Cannot add role: Member is None for role {role.name}")
            return False

        if role in member.roles:
            logging.debug(f"Member {member.name} already has role {role.name}")
            return True

        await member.add_roles(role, reason=reason)
        logging.info(f"Successfully added role '{role.name}' to {member.name}")
        return True

    except discord.NotFound:
        logging.error(f"Role '{role.name}' (ID: {role.id}) not found when adding to {member.name}")
        return False
    except discord.Forbidden:
        logging.error(f"No permission to add role '{role.name}' to {member.name}")
        return False
    except discord.HTTPException as e:
        logging.error(f"HTTP error adding role '{role.name}' to {member.name}: {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error adding role '{role.name}' to {member.name}: {e}")
        return False

async def safe_remove_role(member, role, reason: str = None) -> bool:
    """Safely remove a role from a member with proper error handling"""
    try:
        if not role:
            logging.warning(f"Cannot remove role: Role is None for member {member.name}")
            return False

        if not member:
            logging.warning(f"Cannot remove role: Member is None for role {role.name}")
            return False

        if role not in member.roles:
            logging.debug(f"Member {member.name} doesn't have role {role.name}")
            return True

        await member.remove_roles(role, reason=reason)
        logging.info(f"Successfully removed role '{role.name}' from {member.name}")
        return True

    except discord.NotFound:
        logging.error(f"Role '{role.name}' (ID: {role.id}) not found when removing from {member.name}")
        return False
    except discord.Forbidden:
        logging.error(f"No permission to remove role '{role.name}' from {member.name}")
        return False
    except discord.HTTPException as e:
        logging.error(f"HTTP error removing role '{role.name}' from {member.name}: {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error removing role '{role.name}' from {member.name}: {e}")
        return False

def safe_get_role(guild, role_id) -> object:
    """Safely get a role by ID with proper error handling"""
    try:
        if not guild:
            logging.warning("Cannot get role: Guild is None")
            return None

        if not role_id:
            logging.warning("Cannot get role: Role ID is None")
            return None

        # Convert to int if it's a string
        if isinstance(role_id, str):
            if role_id.isdigit():
                role_id = int(role_id)
            else:
                logging.warning(f"Invalid role ID format: {role_id}")
                return None

        role = guild.get_role(role_id)
        if not role:
            logging.warning(f"Role with ID {role_id} not found in guild {guild.name}")
            return None

        return role

    except Exception as e:
        logging.error(f"Error getting role {role_id} from guild {guild.name if guild else 'None'}: {e}")
        return None

async def validate_role_permissions(guild, role) -> bool:
    """Validate that the bot can manage a specific role"""
    try:
        if not guild or not role:
            return False

        bot_member = guild.me
        if not bot_member:
            logging.warning("Bot member not found in guild")
            return False

        # Check if bot has manage roles permission
        if not bot_member.guild_permissions.manage_roles:
            logging.warning(f"Bot lacks 'Manage Roles' permission in {guild.name}")
            return False

        # Check if bot's highest role is higher than the target role
        bot_top_role = bot_member.top_role
        if bot_top_role.position <= role.position:
            logging.warning(f"Bot's role position ({bot_top_role.position}) is not higher than target role '{role.name}' ({role.position})")
            return False

        return True

    except Exception as e:
        logging.error(f"Error validating role permissions: {e}")
        return False

async def safe_add_reaction(message, emoji_str) -> tuple[bool, str]:
    """Safely add a reaction to a message with proper error handling"""
    try:
        if not message:
            error_msg = "Cannot add reaction: Message is None"
            logging.warning(error_msg)
            return False, error_msg

        if not emoji_str:
            error_msg = "Cannot add reaction: Emoji string is None"
            logging.warning(error_msg)
            return False, error_msg

        await message.add_reaction(emoji_str)
        logging.debug(f"Successfully added reaction {emoji_str} to message {message.id}")
        return True, "Success"

    except discord.NotFound:
        # Provide detailed analysis of why the emoji wasn't found
        error_details = analyze_emoji_error(emoji_str, message.guild)
        logging.warning(f"Emoji not found: {emoji_str} - {error_details}")
        return False, error_details
    except discord.Forbidden:
        error_msg = f"No permission to add reaction {emoji_str} (missing 'Add Reactions' or 'Use External Emojis' permission)"
        logging.error(error_msg)
        return False, error_msg
    except discord.HTTPException as e:
        error_msg = f"Discord API error for emoji {emoji_str}: {e}"
        logging.error(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"Unexpected error adding reaction for emoji {emoji_str}: {e}"
        logging.error(error_msg)
        return False, error_msg

def analyze_emoji_error(emoji_str, guild=None) -> str:
    """Analyze why an emoji failed and provide detailed explanation"""
    try:
        # Check if it's a Unicode emoji
        if len(emoji_str) <= 4 and not emoji_str.startswith('<'):
            return f"Unicode emoji '{emoji_str}' not found (this shouldn't happen)"

        # Check if it's a custom emoji format
        if not (emoji_str.startswith('<') and emoji_str.endswith('>')):
            return f"Invalid emoji format '{emoji_str}' (should be <:name:id> or <a:name:id>)"

        # Parse custom emoji
        parts = emoji_str.split(':')
        if len(parts) < 3:
            return f"Malformed custom emoji '{emoji_str}' (invalid format)"

        is_animated = emoji_str.startswith('<a:')
        emoji_name = parts[1]

        try:
            emoji_id = int(parts[2][:-1])  # Remove the '>' at the end
        except ValueError:
            return f"Invalid emoji ID in '{emoji_str}' (not a number)"

        # Build detailed error message
        emoji_type = "animated" if is_animated else "static"
        error_parts = [
            f"Custom {emoji_type} emoji '{emoji_name}' (ID: {emoji_id}) not found."
        ]

        # Check if it's in the current guild
        if guild:
            guild_emoji = discord.utils.get(guild.emojis, id=emoji_id)
            if guild_emoji:
                return f"Emoji '{emoji_name}' exists in guild but failed to add (possible permission issue)"
            else:
                error_parts.append(f"Emoji not found in server '{guild.name}'.")

                # Check if it might be from another server
                error_parts.append("Possible causes:")
                error_parts.append("• Emoji was deleted from the original server")
                error_parts.append("• Emoji is from a different server (bot needs access)")
                error_parts.append("• Emoji was renamed or modified")
                if is_animated:
                    error_parts.append("• Bot lacks 'Use External Emojis' permission for animated emojis")
        else:
            error_parts.append("Possible causes:")
            error_parts.append("• Emoji was deleted from its original server")
            error_parts.append("• Bot doesn't have access to the emoji's server")
            if is_animated:
                error_parts.append("• Bot lacks permission to use external animated emojis")

        return " ".join(error_parts)

    except Exception as e:
        return f"Failed to analyze emoji error for '{emoji_str}': {e}"

def normalize_emoji_format(emoji_str) -> str:
    """Convert markdown emoji format to Discord emoji format"""
    try:
        # Check if it's in markdown format: [name](https://cdn.discordapp.com/emojis/ID.ext?...)
        if emoji_str.startswith('[') and '](https://cdn.discordapp.com/emojis/' in emoji_str:
            # Extract name and ID from markdown format
            name_end = emoji_str.find('](')
            if name_end > 1:
                name = emoji_str[1:name_end]

                # Extract ID from URL
                url_part = emoji_str[name_end + 2:]
                if '/emojis/' in url_part:
                    id_start = url_part.find('/emojis/') + 8
                    id_end = url_part.find('.', id_start)
                    if id_end > id_start:
                        emoji_id = url_part[id_start:id_end]

                        # Check if it's animated (gif)
                        is_animated = '.gif' in url_part

                        # Convert to Discord format
                        if is_animated:
                            return f"<a:{name}:{emoji_id}>"
                        else:
                            return f"<:{name}:{emoji_id}>"

        # Return as-is if not markdown format
        return emoji_str

    except Exception as e:
        logging.warning(f"Error normalizing emoji format for {emoji_str}: {e}")
        return emoji_str

def validate_emoji(emoji_str, guild=None, strict=True) -> tuple[bool, str]:
    """Validate if an emoji string is valid and accessible"""
    try:
        if not emoji_str:
            return False, "Empty emoji string"

        # Normalize emoji format first
        original_emoji = emoji_str
        emoji_str = normalize_emoji_format(emoji_str)

        # If format was changed, note it
        format_changed = original_emoji != emoji_str
        if format_changed:
            logging.debug(f"Normalized emoji format: {original_emoji} → {emoji_str}")

        # Check if it's a Unicode emoji (simple check)
        if len(emoji_str) <= 4 and not emoji_str.startswith('<'):
            # Unicode emojis are always valid
            return True, "Valid Unicode emoji"

        # Check if it's a custom emoji format <:name:id> or <a:name:id>
        if emoji_str.startswith('<') and emoji_str.endswith('>'):
            # Extract emoji ID from custom emoji format
            parts = emoji_str.split(':')
            if len(parts) < 3:
                return False, "Malformed custom emoji format (should be <:name:id> or <a:name:id>)"

            is_animated = emoji_str.startswith('<a:')
            emoji_name = parts[1]

            try:
                emoji_id = int(parts[-1][:-1])  # Remove the '>' at the end
            except ValueError:
                return False, f"Invalid emoji ID in '{emoji_str}' (not a number)"

            # If guild is provided, check if the emoji exists in the guild
            if guild:
                emoji_obj = discord.utils.get(guild.emojis, id=emoji_id)
                if emoji_obj is not None:
                    return True, f"Valid custom {'animated' if is_animated else 'static'} emoji from this server"
                else:
                    if strict:
                        return False, f"Custom {'animated' if is_animated else 'static'} emoji '{emoji_name}' (ID: {emoji_id}) not found in server '{guild.name}'"
                    else:
                        return False, f"Emoji not accessible in this server"
            else:
                # If no guild provided, we can only validate format
                if strict:
                    return False, "Cannot verify emoji accessibility without server context"
                else:
                    return True, f"Valid custom emoji format (cannot verify accessibility)"

        return False, f"Invalid emoji format '{emoji_str}'"

    except Exception as e:
        logging.error(f"Error validating emoji {emoji_str}: {e}")
        return False, f"Error validating emoji: {e}"

async def validate_emoji_for_reactions(emoji_str, guild, message=None) -> tuple[bool, str]:
    """Strictly validate emoji for reaction role use - actually test if it can be used"""
    try:
        # First do basic validation
        is_valid, validation_msg = validate_emoji(emoji_str, guild, strict=True)
        if not is_valid:
            return False, validation_msg

        # If we have a message, try to actually add the reaction as a test
        if message:
            try:
                await message.add_reaction(emoji_str)
                # If successful, remove the test reaction
                try:
                    await message.clear_reaction(emoji_str)
                except:
                    pass
                return True, "Emoji successfully tested and can be used for reactions"
            except discord.NotFound:
                return False, f"Emoji not found - it may have been deleted or is from an inaccessible server"
            except discord.Forbidden:
                return False, f"No permission to use this emoji (missing 'Add Reactions' or 'Use External Emojis' permission)"
            except discord.HTTPException as e:
                return False, f"Cannot use emoji for reactions: {e}"

        return True, validation_msg

    except Exception as e:
        return False, f"Error testing emoji: {e}"

async def clean_invalid_reactions(message, valid_emojis=None) -> int:
    """Remove invalid reactions from a message and return count of removed reactions"""
    try:
        if not message:
            return 0

        removed_count = 0

        # Get all reactions on the message
        for reaction in message.reactions:
            emoji_str = str(reaction.emoji)

            # If valid_emojis list is provided, check against it
            if valid_emojis is not None:
                if emoji_str not in valid_emojis:
                    try:
                        await reaction.clear()
                        removed_count += 1
                        logging.info(f"Removed invalid reaction {emoji_str} from message {message.id}")
                    except Exception as e:
                        logging.warning(f"Could not remove reaction {emoji_str}: {e}")
            else:
                # Validate emoji exists
                if not validate_emoji(emoji_str, message.guild):
                    try:
                        await reaction.clear()
                        removed_count += 1
                        logging.info(f"Removed invalid reaction {emoji_str} from message {message.id}")
                    except Exception as e:
                        logging.warning(f"Could not remove reaction {emoji_str}: {e}")

        return removed_count

    except Exception as e:
        logging.error(f"Error cleaning invalid reactions: {e}")
        return 0

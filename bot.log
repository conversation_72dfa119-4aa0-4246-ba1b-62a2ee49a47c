2025-02-28 21:50:48,082 - INFO - logging in using static token
2025-02-28 21:50:50,662 - INFO - Shard ID None has connected to Gateway (Session ID: 0e2da6bb27a7fc49aada7b48f4d8a9a5).
2025-02-28 21:51:16,210 - INFO - Data saved successfully to MongoDB
2025-02-28 21:52:07,359 - INFO - Permission Check - Command: setup_reaction_roles
2025-02-28 21:52:07,359 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-02-28 21:52:07,359 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-02-28 21:52:07,359 - INFO - Has Admin Permission: True
2025-02-28 21:52:17,447 - INFO - Data saved successfully to MongoDB
2025-02-28 21:57:21,726 - INFO - logging in using static token
2025-02-28 21:57:24,786 - INFO - Shard ID None has connected to Gateway (Session ID: f2c4abc1d2791cb06060e67aabfed3d1).
2025-02-28 21:58:23,520 - INFO - Data saved successfully to MongoDB
2025-02-28 21:58:48,274 - INFO - Permission Check - Command: setup_reaction_roles
2025-02-28 21:58:48,274 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-02-28 21:58:48,274 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-02-28 21:58:48,274 - INFO - Has Admin Permission: True
2025-02-28 21:58:57,623 - INFO - Data saved successfully to MongoDB
2025-02-28 21:59:13,656 - INFO - Data saved successfully to MongoDB
2025-03-01 11:52:35,852 - INFO - logging in using static token
2025-03-01 11:52:38,009 - INFO - Shard ID None has connected to Gateway (Session ID: efc2f16df9168b78d89c349882e736ba).
2025-03-01 11:53:41,178 - INFO - logging in using static token
2025-03-01 11:53:43,589 - INFO - Shard ID None has connected to Gateway (Session ID: 90707bc5de9fdd561d2dc260acb4ceef).
2025-03-01 11:54:13,623 - ERROR - Error in VanityNotificationModal.on_submit: 'coroutine' object is not subscriptable
2025-03-01 11:55:19,495 - INFO - logging in using static token
2025-03-01 11:55:21,600 - INFO - Shard ID None has connected to Gateway (Session ID: 970363fe19fdabcebcc6465c09bc88ee).
2025-03-01 11:58:15,264 - INFO - logging in using static token
2025-03-01 11:58:17,532 - INFO - Shard ID None has connected to Gateway (Session ID: 4dd3c92c3eb6208bf0dcbc7cea94337e).
2025-03-01 11:58:51,369 - INFO - Data saved successfully to MongoDB
2025-03-01 11:59:10,176 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-01 11:59:10,176 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-01 11:59:10,176 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-01 11:59:10,176 - INFO - Has Admin Permission: True
2025-03-01 11:59:17,320 - INFO - Data saved successfully to MongoDB
2025-03-01 12:00:28,710 - INFO - logging in using static token
2025-03-01 12:00:31,131 - INFO - Shard ID None has connected to Gateway (Session ID: 7d18ccd5672ef8807ff279da41d57c8e).
2025-03-01 12:07:13,790 - INFO - logging in using static token
2025-03-01 12:07:16,164 - INFO - Shard ID None has connected to Gateway (Session ID: f1fa7319398cb1e6e157a18b4ca3fdfc).
2025-03-01 12:10:00,360 - INFO - logging in using static token
2025-03-01 12:10:02,875 - INFO - Shard ID None has connected to Gateway (Session ID: d6f486d7ae1651517a5269fcc5345043).
2025-03-01 12:10:32,193 - INFO - Data saved successfully to MongoDB
2025-03-01 12:13:22,719 - INFO - logging in using static token
2025-03-01 12:13:29,177 - INFO - Shard ID None has connected to Gateway (Session ID: 3bccef44b712a301f031ff80894dbf5b).
2025-03-01 12:13:45,451 - INFO - Data saved successfully to MongoDB
2025-03-01 23:34:36,255 - INFO - logging in using static token
2025-03-01 23:34:38,894 - INFO - Shard ID None has connected to Gateway (Session ID: 36c9510ec2778533944d190cab5b7ede).
2025-03-01 23:35:56,354 - INFO - Data saved successfully to MongoDB
2025-03-01 23:36:13,666 - INFO - Data saved successfully to MongoDB
2025-03-01 23:36:29,914 - INFO - Data saved successfully to MongoDB
2025-03-01 23:38:56,784 - INFO - logging in using static token
2025-03-01 23:38:59,290 - INFO - Shard ID None has connected to Gateway (Session ID: 8e93ae9e70988fad351bc8b66939c80b).
2025-03-01 23:46:36,982 - INFO - logging in using static token
2025-03-01 23:46:39,577 - INFO - Shard ID None has connected to Gateway (Session ID: 53ad2aa88ecf4450acfe1dbc69ebdb8d).
2025-03-01 23:46:53,953 - INFO - Data saved successfully to MongoDB
2025-03-01 23:47:09,873 - INFO - Data saved successfully to MongoDB
2025-03-01 23:47:20,544 - INFO - Data saved successfully to MongoDB
2025-03-01 23:48:32,971 - INFO - logging in using static token
2025-03-01 23:48:35,383 - INFO - Shard ID None has connected to Gateway (Session ID: 67ffd2144456efcbed311876d0c134b3).
2025-03-01 23:53:44,668 - INFO - logging in using static token
2025-03-01 23:53:47,481 - INFO - Shard ID None has connected to Gateway (Session ID: 7c34183857a7837538db28325be6bd9c).
2025-03-01 23:54:22,947 - INFO - Data saved successfully to MongoDB
2025-03-01 23:54:35,552 - INFO - logging in using static token
2025-03-01 23:54:37,890 - INFO - Shard ID None has connected to Gateway (Session ID: d7e3aff38910f7cebc312336de38f648).
2025-03-02 00:12:51,068 - INFO - Shard ID None has successfully RESUMED session d7e3aff38910f7cebc312336de38f648.
2025-03-02 00:16:36,066 - INFO - logging in using static token
2025-03-02 00:16:38,904 - INFO - Shard ID None has connected to Gateway (Session ID: 2a3f838197086b40083b11cb8ad855fc).
2025-03-02 00:19:51,302 - INFO - logging in using static token
2025-03-02 00:19:55,388 - INFO - Shard ID None has connected to Gateway (Session ID: 5c59ceabe0f87a1fd4da0e83225ad5a8).
2025-03-02 00:21:31,399 - INFO - logging in using static token
2025-03-02 00:21:33,720 - INFO - Shard ID None has connected to Gateway (Session ID: 4434011a527c50ce307f22d729dfe8d5).
2025-03-02 00:21:54,195 - INFO - Data saved successfully to MongoDB
2025-03-02 00:22:07,087 - INFO - logging in using static token
2025-03-02 00:22:10,183 - INFO - Shard ID None has connected to Gateway (Session ID: 4328c1a4aa46e027b3b192a49e52dda4).
2025-03-02 00:28:43,599 - INFO - logging in using static token
2025-03-02 00:28:46,289 - INFO - Shard ID None has connected to Gateway (Session ID: 31f72bdf6d12b20a0995e48c2235593e).
2025-03-02 00:29:32,096 - INFO - logging in using static token
2025-03-02 00:29:34,239 - INFO - Shard ID None has connected to Gateway (Session ID: 7d98854e1957d6205ba1231236fd3982).
2025-03-02 00:40:18,735 - INFO - logging in using static token
2025-03-02 00:40:21,562 - INFO - Shard ID None has connected to Gateway (Session ID: 06f74f278a199d3fd1a793de552928e1).
2025-03-02 00:42:59,032 - INFO - logging in using static token
2025-03-02 00:43:01,441 - INFO - Shard ID None has connected to Gateway (Session ID: da09c7029f2f886b48fc17c412d0e0d5).
2025-03-02 00:44:26,365 - INFO - logging in using static token
2025-03-02 00:44:28,677 - INFO - Shard ID None has connected to Gateway (Session ID: 12a83caf1968bee8a733a23d9a1391fd).
2025-03-02 00:44:59,137 - INFO - Data saved successfully to MongoDB
2025-03-02 16:12:12,028 - INFO - logging in using static token
2025-03-02 16:12:15,416 - INFO - Shard ID None has connected to Gateway (Session ID: f1455bce4ea0e4d3f6e83da6ae22a305).
2025-03-02 16:13:09,695 - INFO - logging in using static token
2025-03-02 16:13:12,283 - INFO - Shard ID None has connected to Gateway (Session ID: ba1c11c8a88e6aeaeca3c7fef34591d5).
2025-03-02 16:26:49,131 - INFO - logging in using static token
2025-03-02 16:26:51,735 - INFO - Shard ID None has connected to Gateway (Session ID: 76386cea001e8629ab32394008a344a9).
2025-03-02 16:27:38,959 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-02 16:27:38,959 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-02 16:27:38,959 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-02 16:27:38,959 - INFO - Has Admin Permission: True
2025-03-02 16:28:04,812 - INFO - Data saved successfully to MongoDB
2025-03-02 16:28:28,930 - INFO - logging in using static token
2025-03-02 16:28:31,282 - INFO - Shard ID None has connected to Gateway (Session ID: dff18557dc4d85289cb91a5655bb70ba).
2025-03-02 16:31:41,011 - INFO - logging in using static token
2025-03-02 16:31:43,315 - INFO - Shard ID None has connected to Gateway (Session ID: e90ccbdcc915246ae4e3ea4c979bfd5b).
2025-03-04 00:20:20,428 - INFO - logging in using static token
2025-03-04 00:20:23,174 - INFO - Shard ID None has connected to Gateway (Session ID: de28b81c4420a21109e24174dfbbc49e).
2025-03-04 00:23:03,998 - INFO - logging in using static token
2025-03-04 00:23:06,614 - INFO - Shard ID None has connected to Gateway (Session ID: dd95dd7741d01ca6b69f3fda368afb07).
2025-03-04 00:23:27,015 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-04 00:23:27,015 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-04 00:23:27,015 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-04 00:23:27,015 - INFO - Has Admin Permission: True
2025-03-04 00:23:38,878 - INFO - Data saved successfully to MongoDB
2025-03-04 00:31:25,559 - INFO - logging in using static token
2025-03-04 00:31:28,230 - INFO - Shard ID None has connected to Gateway (Session ID: c226a72ff380575630b304f09f3406f1).
2025-03-04 00:33:08,848 - INFO - logging in using static token
2025-03-04 00:33:11,451 - INFO - Shard ID None has connected to Gateway (Session ID: 00993acdc2c15bd0eb602bd705c1b802).
2025-03-04 00:33:32,829 - INFO - logging in using static token
2025-03-04 00:33:35,206 - INFO - Shard ID None has connected to Gateway (Session ID: d32f288c7ebbee8c17a25808f8d663fd).
2025-03-04 00:34:37,599 - INFO - Shard ID None has successfully RESUMED session d32f288c7ebbee8c17a25808f8d663fd.
2025-03-04 00:40:26,682 - INFO - logging in using static token
2025-03-04 00:40:29,380 - INFO - Shard ID None has connected to Gateway (Session ID: fa72fcaa23a4197a527e347d21873c14).
2025-03-04 00:40:52,042 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-04 00:40:52,042 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-04 00:40:52,042 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-04 00:40:52,043 - INFO - Has Admin Permission: True
2025-03-04 00:40:59,445 - INFO - Data saved successfully to MongoDB
2025-03-04 00:44:48,342 - INFO - logging in using static token
2025-03-04 00:44:50,559 - INFO - Shard ID None has connected to Gateway (Session ID: b8d9ed8968f5c228bff27fedcfb7ba89).
2025-03-04 00:46:04,329 - INFO - Permission Check - Command: setup_reaction_roles
2025-03-04 00:46:04,330 - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-03-04 00:46:04,330 - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-03-04 00:46:04,330 - INFO - Has Admin Permission: True
2025-03-04 00:46:17,788 - INFO - Data saved successfully to MongoDB
2025-03-04 00:46:28,482 - INFO - logging in using static token
2025-03-04 00:46:31,006 - INFO - Shard ID None has connected to Gateway (Session ID: 29397ac1b6760169fd788ba660d484a7).
2025-03-04 00:46:43,705 - ERROR - Ignoring exception in on_raw_reaction_add
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 449, in _run_event
    await coro(*args, **kwargs)
  File "F:\MissMinutesBotmain\bot.py", line 3206, in on_raw_reaction_add
    if emoji in reaction_roles["roles"]:
                ~~~~~~~~~~~~~~^^^^^^^^^
KeyError: 'roles'
2025-03-04 00:46:49,936 - ERROR - Ignoring exception in on_raw_reaction_remove
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 449, in _run_event
    await coro(*args, **kwargs)
  File "F:\MissMinutesBotmain\bot.py", line 3237, in on_raw_reaction_remove
    if emoji in reaction_roles["roles"]:
                ~~~~~~~~~~~~~~^^^^^^^^^
KeyError: 'roles'
2025-03-04 00:46:50,515 - ERROR - Ignoring exception in on_raw_reaction_add
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\client.py", line 449, in _run_event
    await coro(*args, **kwargs)
  File "F:\MissMinutesBotmain\bot.py", line 3206, in on_raw_reaction_add
    if emoji in reaction_roles["roles"]:
                ~~~~~~~~~~~~~~^^^^^^^^^
KeyError: 'roles'
2025-03-04 00:55:57,326 - INFO - logging in using static token
2025-03-04 00:55:59,999 - INFO - Shard ID None has connected to Gateway (Session ID: 834b55b62c82717eae39014e4b9e01b5).
2025-03-04 00:58:23,297 - INFO - logging in using static token
2025-03-04 00:58:25,663 - INFO - Shard ID None has connected to Gateway (Session ID: 071577352cecb3c769ad777fa03ba1ad).
2025-06-04 03:55:47,106 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 03:55:47,132 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 03:55:47,132 - root - INFO - Initializing performance optimization systems...
2025-06-04 03:55:52,185 - enhanced_database - ERROR - Failed to connect to MongoDB: 
2025-06-04 03:55:52,186 - root - INFO - Bot Status: Performance optimization initialization failed: Failed to connect to database
2025-06-04 03:55:52,186 - root - WARNING - Performance optimization initialization failed: Failed to connect to database
2025-06-04 03:55:52,186 - root - INFO - Memory manager started
2025-06-04 03:55:52,186 - root - INFO - Loading ticket configuration...
2025-06-04 03:56:22,566 - root - WARNING - Failed to load ticket configuration
2025-06-04 03:56:22,566 - root - INFO - Loading bot data...
2025-06-04 03:56:22,566 - enhanced_database - WARNING - Collection settings not found
2025-06-04 03:56:22,566 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 03:56:22,566 - root - INFO - Falling back to regular database for data loading
2025-06-04 03:56:22,566 - root - INFO - Loading bot data from MongoDB...
2025-06-04 03:56:22,567 - root - ERROR - Error in data loading: name 'pymongo' is not defined
Traceback (most recent call last):
  File "D:\MissMinutesBotmain\bot.py", line 5235, in load_data
    client = pymongo.MongoClient("mongodb://localhost:27017/")
             ^^^^^^^
NameError: name 'pymongo' is not defined
2025-06-04 03:56:22,569 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 03:56:22,569 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 03:56:23,482 - root - INFO - Synced 32 command(s)!
2025-06-04 03:56:23,483 - root - INFO - Started vanity status check task
2025-06-04 03:56:23,483 - root - INFO - Restored application buttons
2025-06-04 03:56:23,483 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 03:56:23,484 - root - WARNING - No reaction role channel or message ID configured
2025-06-04 03:56:23,485 - root - INFO - Restored reaction roles panel
2025-06-04 03:56:23,485 - root - INFO - No expired gang invitations found
2025-06-04 03:56:23,485 - root - INFO - Cleaned up expired gang invitations
2025-06-04 03:56:23,485 - root - INFO - Starting gang invitation view restoration...
2025-06-04 03:56:23,485 - root - INFO - Found 0 pending gang invitations
2025-06-04 03:56:23,485 - root - INFO - No pending gang invitations to restore
2025-06-04 03:56:23,485 - root - INFO - Restored gang invitation views
2025-06-04 03:56:23,485 - root - INFO - Using already loaded reaction role data: message_id=None, channel_id=None
2025-06-04 03:56:23,485 - root - INFO - Reaction roles structure: {}
2025-06-04 03:56:23,485 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 03:56:23,485 - root - INFO - Bot initialization completed!
2025-06-04 03:56:28,526 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f87ed6558002f9c89e6, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:28,527 - database - ERROR - Failed to connect to database
2025-06-04 03:56:33,535 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f8ced6558002f9c89e7, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:33,535 - database - ERROR - Failed to connect to database
2025-06-04 03:56:38,543 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f91ed6558002f9c89e8, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:38,543 - database - ERROR - Failed to connect to database
2025-06-04 03:56:43,551 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f96ed6558002f9c89e9, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:43,552 - database - ERROR - Failed to connect to database
2025-06-04 03:56:48,564 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6f9bed6558002f9c89ea, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:48,564 - database - ERROR - Failed to connect to database
2025-06-04 03:56:53,575 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fa0ed6558002f9c89eb, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:53,575 - database - ERROR - Failed to connect to database
2025-06-04 03:56:58,587 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fa5ed6558002f9c89ec, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:56:58,588 - database - ERROR - Failed to connect to database
2025-06-04 03:57:03,600 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6faaed6558002f9c89ed, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:03,600 - database - ERROR - Failed to connect to database
2025-06-04 03:57:08,611 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fafed6558002f9c89ee, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:08,611 - database - ERROR - Failed to connect to database
2025-06-04 03:57:13,630 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fb4ed6558002f9c89ef, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:13,630 - database - ERROR - Failed to connect to database
2025-06-04 03:57:18,641 - database - ERROR - Error connecting to MongoDB: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms), Timeout: 5.0s, Topology Description: <TopologyDescription id: 683f6fb9ed6558002f9c89f0, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 5000.0ms, connectTimeoutMS: 5000.0ms)')>]>
2025-06-04 03:57:18,641 - database - ERROR - Failed to connect to database
2025-06-04 03:58:00,813 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 03:58:00,813 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 03:58:00,813 - root - INFO - Initializing performance optimization systems...
2025-06-04 03:58:00,841 - root - INFO - Enhanced database connected successfully
2025-06-04 03:58:00,852 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 03:58:00,852 - root - INFO - Memory manager started
2025-06-04 03:58:00,852 - root - INFO - Loading ticket configuration...
2025-06-04 03:58:00,935 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 03:58:00,935 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 03:58:02,203 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379580040001355847
2025-06-04 03:58:02,204 - root - INFO - Loading bot data...
2025-06-04 03:58:02,210 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 03:58:02,220 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 03:58:02,220 - root - INFO - Falling back to regular database for data loading
2025-06-04 03:58:02,220 - root - INFO - Loading bot data from MongoDB...
2025-06-04 03:58:02,220 - root - ERROR - Error in data loading: name 'pymongo' is not defined
Traceback (most recent call last):
  File "D:\MissMinutesBotmain\bot.py", line 5235, in load_data
    client = pymongo.MongoClient("mongodb://localhost:27017/")
             ^^^^^^^
NameError: name 'pymongo' is not defined
2025-06-04 03:58:02,223 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 03:58:02,223 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 03:58:02,834 - root - INFO - Synced 32 command(s)!
2025-06-04 03:58:02,834 - root - INFO - Started vanity status check task
2025-06-04 03:58:02,834 - root - INFO - Restored application buttons
2025-06-04 03:58:02,834 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 03:58:02,834 - root - WARNING - No reaction role channel or message ID configured
2025-06-04 03:58:02,835 - root - INFO - Restored reaction roles panel
2025-06-04 03:58:02,835 - root - INFO - No expired gang invitations found
2025-06-04 03:58:02,835 - root - INFO - Cleaned up expired gang invitations
2025-06-04 03:58:02,835 - root - INFO - Starting gang invitation view restoration...
2025-06-04 03:58:02,835 - root - INFO - Found 0 pending gang invitations
2025-06-04 03:58:02,835 - root - INFO - No pending gang invitations to restore
2025-06-04 03:58:02,835 - root - INFO - Restored gang invitation views
2025-06-04 03:58:02,835 - root - INFO - Using already loaded reaction role data: message_id=None, channel_id=None
2025-06-04 03:58:02,835 - root - INFO - Reaction roles structure: {}
2025-06-04 03:58:02,835 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 03:58:02,835 - root - INFO - Bot initialization completed!
2025-06-04 04:00:23,606 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:00:23,606 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:00:23,606 - root - INFO - Initializing performance optimization systems...
2025-06-04 04:00:23,628 - root - INFO - Enhanced database connected successfully
2025-06-04 04:00:23,628 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 04:00:23,628 - root - INFO - Memory manager started
2025-06-04 04:00:23,628 - root - INFO - Loading ticket configuration...
2025-06-04 04:00:23,631 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 04:00:23,632 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 04:00:25,064 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379580639350882304
2025-06-04 04:00:25,064 - root - INFO - Loading bot data...
2025-06-04 04:00:25,065 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 04:00:25,066 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 04:00:25,067 - root - INFO - Falling back to regular database for data loading
2025-06-04 04:00:25,067 - root - INFO - Loading bot data from MongoDB...
2025-06-04 04:00:25,067 - root - ERROR - Error in data loading: name 'pymongo' is not defined
Traceback (most recent call last):
  File "D:\MissMinutesBotmain\bot.py", line 5235, in load_data
    client = pymongo.MongoClient("mongodb://localhost:27017/")
             ^^^^^^^
NameError: name 'pymongo' is not defined
2025-06-04 04:00:25,069 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 04:00:25,069 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 04:00:25,627 - root - INFO - Synced 32 command(s)!
2025-06-04 04:00:25,627 - root - INFO - Started vanity status check task
2025-06-04 04:00:25,627 - root - INFO - Restored application buttons
2025-06-04 04:00:25,627 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 04:00:25,627 - root - WARNING - No reaction role channel or message ID configured
2025-06-04 04:00:25,627 - root - INFO - Restored reaction roles panel
2025-06-04 04:00:25,627 - root - INFO - No expired gang invitations found
2025-06-04 04:00:25,627 - root - INFO - Cleaned up expired gang invitations
2025-06-04 04:00:25,627 - root - INFO - Starting gang invitation view restoration...
2025-06-04 04:00:25,627 - root - INFO - Found 0 pending gang invitations
2025-06-04 04:00:25,627 - root - INFO - No pending gang invitations to restore
2025-06-04 04:00:25,627 - root - INFO - Restored gang invitation views
2025-06-04 04:00:25,627 - root - INFO - Using already loaded reaction role data: message_id=None, channel_id=None
2025-06-04 04:00:25,627 - root - INFO - Reaction roles structure: {}
2025-06-04 04:00:25,628 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 04:00:25,628 - root - INFO - Bot initialization completed!
2025-06-04 04:02:49,351 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:02:49,351 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:02:49,351 - root - INFO - Initializing performance optimization systems...
2025-06-04 04:02:49,370 - root - INFO - Enhanced database connected successfully
2025-06-04 04:02:49,370 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 04:02:49,371 - root - INFO - Memory manager started
2025-06-04 04:02:49,371 - root - INFO - Loading ticket configuration...
2025-06-04 04:02:49,374 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 04:02:49,374 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 04:02:50,991 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379581250947387492
2025-06-04 04:02:50,992 - root - INFO - Loading bot data...
2025-06-04 04:02:50,993 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 04:02:50,994 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 04:02:50,994 - root - INFO - Falling back to regular database for data loading
2025-06-04 04:02:50,994 - root - INFO - Loading bot data from MongoDB...
2025-06-04 04:02:51,262 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 04:02:51,262 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 04:02:51,262 - root - INFO - Loaded 1 sticky messages
2025-06-04 04:02:51,262 - root - INFO - Loaded reaction roles: 2 roles, MessageID=1366685068696879194, ChannelID=1363261478844502097
2025-06-04 04:02:51,262 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 04:02:51,262 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 04:02:51,262 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 04:02:51,751 - root - INFO - Synced 32 command(s)!
2025-06-04 04:02:51,751 - root - INFO - Started vanity status check task
2025-06-04 04:02:51,751 - root - INFO - Restored application buttons
2025-06-04 04:02:51,751 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 04:02:51,751 - root - INFO - Found reaction role channel: crimson1
2025-06-04 04:02:52,183 - root - INFO - Found existing reaction role message: 1366685068696879194
2025-06-04 04:02:52,813 - root - INFO - Cleared existing reactions
2025-06-04 04:02:52,813 - root - INFO - Adding reaction role: ✅ -> 'level' 1
2025-06-04 04:02:53,455 - root - INFO - Adding reaction role: ❌ -> 'level' 10
2025-06-04 04:02:54,601 - root - INFO - Reaction roles panel restored successfully
2025-06-04 04:02:54,601 - root - INFO - Restored reaction roles panel
2025-06-04 04:02:54,601 - root - INFO - No expired gang invitations found
2025-06-04 04:02:54,601 - root - INFO - Cleaned up expired gang invitations
2025-06-04 04:02:54,601 - root - INFO - Starting gang invitation view restoration...
2025-06-04 04:02:54,601 - root - INFO - Found 1 pending gang invitations
2025-06-04 04:02:54,601 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 04:02:54,601 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 04:02:54,601 - root - INFO - Restored gang invitation views
2025-06-04 04:02:56,030 - root - INFO - Recreated application dropdown menu
2025-06-04 04:02:56,030 - root - INFO - Using already loaded reaction role data: message_id=1366685068696879194, channel_id=1363261478844502097
2025-06-04 04:02:56,030 - root - INFO - Reaction roles structure: {'roles': {'✅': 1363215768015208551, '❌': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:02:56,030 - root - INFO - Found channel for reaction roles: crimson1
2025-06-04 04:02:56,407 - root - INFO - Found existing reaction message: 1366685068696879194
2025-06-04 04:02:57,066 - root - INFO - Reaction roles content: {'roles': {'✅': 1363215768015208551, '❌': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:02:57,066 - root - INFO - Using loaded reaction_roles: {'roles': {'✅': 1363215768015208551, '❌': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:02:57,066 - root - INFO - Using reaction roles: {'✅': 1363215768015208551, '❌': 1363215788227694863}
2025-06-04 04:02:57,066 - root - INFO - Processing role: emoji=✅, role_id=1363215768015208551, type=<class 'bson.int64.Int64'>
2025-06-04 04:02:57,066 - root - INFO - Found role: 'level' 1
2025-06-04 04:02:57,796 - root - INFO - Processing role: emoji=❌, role_id=1363215788227694863, type=<class 'bson.int64.Int64'>
2025-06-04 04:02:57,806 - root - INFO - Found role: 'level' 10
2025-06-04 04:02:58,869 - root - INFO - Recreated reaction role message
2025-06-04 04:02:58,869 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 04:02:58,869 - root - INFO - Bot initialization completed!
2025-06-04 04:03:33,981 - root - INFO - Permission Check - Command: setup_reaction_roles
2025-06-04 04:03:33,981 - root - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-06-04 04:03:33,981 - root - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-06-04 04:03:33,981 - root - INFO - Has Admin Permission: True
2025-06-04 04:03:34,555 - root - INFO - Created new reaction roles message with ID: 1379581434154713148 in channel: 1367069308164902913
2025-06-04 04:03:34,555 - root - INFO - Saving reaction roles data...
2025-06-04 04:03:34,603 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 04:03:44,183 - root - INFO - Adding reaction role: emoji=[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife), role='level' 10 (ID: 1363215788227694863)
2025-06-04 04:03:44,183 - root - INFO - Saving reaction roles data...
2025-06-04 04:03:44,188 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 04:08:49,463 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:08:49,463 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:08:49,463 - root - INFO - Initializing performance optimization systems...
2025-06-04 04:08:49,481 - root - INFO - Enhanced database connected successfully
2025-06-04 04:08:49,481 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 04:08:49,481 - root - INFO - Memory manager started
2025-06-04 04:08:49,481 - root - INFO - Loading ticket configuration...
2025-06-04 04:08:49,484 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 04:08:49,484 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 04:08:51,596 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379582763304357968
2025-06-04 04:08:51,596 - root - INFO - Loading bot data...
2025-06-04 04:08:51,598 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 04:08:51,599 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 04:08:51,599 - root - INFO - Falling back to regular database for data loading
2025-06-04 04:08:51,599 - root - INFO - Loading bot data from MongoDB...
2025-06-04 04:08:51,605 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 04:08:51,605 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 04:08:51,605 - root - INFO - Loaded 1 sticky messages
2025-06-04 04:08:51,605 - root - INFO - Loaded reaction roles: 1 roles, MessageID=1379581434154713148, ChannelID=1367069308164902913
2025-06-04 04:08:51,605 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 04:08:51,605 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 04:08:51,606 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 04:08:52,115 - root - INFO - Synced 33 command(s)!
2025-06-04 04:08:52,115 - root - INFO - Started vanity status check task
2025-06-04 04:08:52,115 - root - INFO - Restored application buttons
2025-06-04 04:08:52,116 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 04:08:52,116 - root - INFO - Found reaction role channel: test6
2025-06-04 04:08:52,471 - root - INFO - Found existing reaction role message: 1379581434154713148
2025-06-04 04:08:53,073 - root - INFO - Cleared existing reactions
2025-06-04 04:08:53,073 - root - INFO - Adding reaction role: [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) -> 'level' 10
2025-06-04 04:08:53,403 - root - ERROR - Error adding reaction for emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife): 404 Not Found (error code: 0): 404: Not Found
2025-06-04 04:08:53,778 - root - INFO - Reaction roles panel restored successfully
2025-06-04 04:08:53,778 - root - INFO - Restored reaction roles panel
2025-06-04 04:08:53,778 - root - INFO - No expired gang invitations found
2025-06-04 04:08:53,778 - root - INFO - Cleaned up expired gang invitations
2025-06-04 04:08:53,778 - root - INFO - Starting gang invitation view restoration...
2025-06-04 04:08:53,778 - root - INFO - Found 1 pending gang invitations
2025-06-04 04:08:53,779 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 04:08:53,779 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 04:08:53,779 - root - INFO - Restored gang invitation views
2025-06-04 04:08:55,221 - root - INFO - Recreated application dropdown menu
2025-06-04 04:08:55,221 - root - INFO - Using already loaded reaction role data: message_id=1379581434154713148, channel_id=1367069308164902913
2025-06-04 04:08:55,221 - root - INFO - Reaction roles structure: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:08:55,221 - root - INFO - Found channel for reaction roles: test6
2025-06-04 04:08:55,686 - root - INFO - Found existing reaction message: 1379581434154713148
2025-06-04 04:08:56,347 - root - INFO - Reaction roles content: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:08:56,347 - root - INFO - Using loaded reaction_roles: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:08:56,347 - root - INFO - Using reaction roles: {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}
2025-06-04 04:08:56,347 - root - INFO - Processing role: emoji=[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife), role_id=1363215788227694863, type=<class 'bson.int64.Int64'>
2025-06-04 04:08:56,347 - root - INFO - Found role: 'level' 10
2025-06-04 04:08:56,680 - root - ERROR - Error recreating reaction role message: 404 Not Found (error code: 0): 404: Not Found
2025-06-04 04:08:56,734 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 04:08:56,734 - root - INFO - Bot initialization completed!
2025-06-04 04:24:55,926 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:24:55,926 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:24:55,927 - root - INFO - Initializing performance optimization systems...
2025-06-04 04:24:56,084 - root - INFO - Enhanced database connected successfully
2025-06-04 04:24:56,084 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 04:24:56,084 - root - INFO - Memory manager started
2025-06-04 04:24:56,084 - root - INFO - Loading ticket configuration...
2025-06-04 04:24:56,099 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 04:24:56,099 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 04:24:57,473 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379586814859280394
2025-06-04 04:24:57,474 - root - INFO - Loading bot data...
2025-06-04 04:24:57,475 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 04:24:57,476 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 04:24:57,476 - root - INFO - Falling back to regular database for data loading
2025-06-04 04:24:57,476 - root - INFO - Loading bot data from MongoDB...
2025-06-04 04:24:57,485 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 04:24:57,485 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 04:24:57,485 - root - INFO - Loaded 1 sticky messages
2025-06-04 04:24:57,485 - root - INFO - Loaded reaction roles: 1 roles, MessageID=1379581434154713148, ChannelID=1367069308164902913
2025-06-04 04:24:57,485 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 04:24:57,486 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 04:24:57,486 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 04:24:58,024 - root - INFO - Synced 34 command(s)!
2025-06-04 04:24:58,024 - root - INFO - Started vanity status check task
2025-06-04 04:24:58,024 - root - INFO - Restored application buttons
2025-06-04 04:24:58,024 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 04:24:58,024 - root - INFO - Found reaction role channel: test6
2025-06-04 04:24:58,440 - root - INFO - Found existing reaction role message: 1379581434154713148
2025-06-04 04:24:59,198 - root - INFO - Cleared existing reactions
2025-06-04 04:24:59,198 - root - INFO - Adding reaction role: [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) -> 'level' 10
2025-06-04 04:24:59,534 - root - ERROR - Error adding reaction for emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife): 404 Not Found (error code: 0): 404: Not Found
2025-06-04 04:25:00,107 - root - INFO - Reaction roles panel restored successfully
2025-06-04 04:25:00,107 - root - INFO - Restored reaction roles panel
2025-06-04 04:25:00,107 - root - INFO - No expired gang invitations found
2025-06-04 04:25:00,107 - root - INFO - Cleaned up expired gang invitations
2025-06-04 04:25:00,108 - root - INFO - Starting gang invitation view restoration...
2025-06-04 04:25:00,108 - root - INFO - Found 1 pending gang invitations
2025-06-04 04:25:00,108 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 04:25:00,108 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 04:25:00,108 - root - INFO - Restored gang invitation views
2025-06-04 04:25:01,478 - root - INFO - Recreated application dropdown menu
2025-06-04 04:25:01,478 - root - INFO - Using already loaded reaction role data: message_id=1379581434154713148, channel_id=1367069308164902913
2025-06-04 04:25:01,478 - root - INFO - Reaction roles structure: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:25:01,478 - root - INFO - Found channel for reaction roles: test6
2025-06-04 04:25:01,950 - root - INFO - Found existing reaction message: 1379581434154713148
2025-06-04 04:25:02,614 - root - INFO - Reaction roles content: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:25:02,614 - root - INFO - Using loaded reaction_roles: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:25:02,614 - root - INFO - Using reaction roles: {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}
2025-06-04 04:25:02,614 - root - INFO - Processing role: emoji=[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife), role_id=1363215788227694863, type=<class 'bson.int64.Int64'>
2025-06-04 04:25:02,614 - root - WARNING - Invalid emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) for role 'level' 10, skipping
2025-06-04 04:25:02,967 - root - INFO - Recreated reaction role message
2025-06-04 04:25:02,967 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 04:25:02,967 - root - INFO - Bot initialization completed!
2025-06-04 04:30:32,375 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:30:32,376 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:30:32,376 - root - INFO - Initializing performance optimization systems...
2025-06-04 04:30:32,394 - root - INFO - Enhanced database connected successfully
2025-06-04 04:30:32,394 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 04:30:32,394 - root - INFO - Memory manager started
2025-06-04 04:30:32,394 - root - INFO - Loading ticket configuration...
2025-06-04 04:30:32,397 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 04:30:32,397 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 04:30:33,580 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379588224791347341
2025-06-04 04:30:33,580 - root - INFO - Loading bot data...
2025-06-04 04:30:33,581 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 04:30:33,582 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 04:30:33,582 - root - INFO - Falling back to regular database for data loading
2025-06-04 04:30:33,582 - root - INFO - Loading bot data from MongoDB...
2025-06-04 04:30:33,588 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 04:30:33,588 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 04:30:33,588 - root - INFO - Loaded 1 sticky messages
2025-06-04 04:30:33,588 - root - INFO - Loaded reaction roles: 1 roles, MessageID=1379581434154713148, ChannelID=1367069308164902913
2025-06-04 04:30:33,588 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 04:30:33,588 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 04:30:33,588 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 04:30:34,715 - root - INFO - Synced 34 command(s)!
2025-06-04 04:30:34,715 - root - INFO - Started vanity status check task
2025-06-04 04:30:34,715 - root - INFO - Restored application buttons
2025-06-04 04:30:34,715 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 04:30:34,715 - root - INFO - Found reaction role channel: test6
2025-06-04 04:30:35,135 - root - INFO - Found existing reaction role message: 1379581434154713148
2025-06-04 04:30:35,749 - root - INFO - Cleared existing reactions
2025-06-04 04:30:35,749 - root - INFO - Adding reaction role: [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) -> 'level' 10
2025-06-04 04:30:36,110 - root - ERROR - Error adding reaction for emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife): 404 Not Found (error code: 0): 404: Not Found
2025-06-04 04:30:36,495 - root - INFO - Reaction roles panel restored successfully
2025-06-04 04:30:36,495 - root - INFO - Restored reaction roles panel
2025-06-04 04:30:36,495 - root - INFO - No expired gang invitations found
2025-06-04 04:30:36,495 - root - INFO - Cleaned up expired gang invitations
2025-06-04 04:30:36,495 - root - INFO - Starting gang invitation view restoration...
2025-06-04 04:30:36,495 - root - INFO - Found 1 pending gang invitations
2025-06-04 04:30:36,495 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 04:30:36,495 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 04:30:36,495 - root - INFO - Restored gang invitation views
2025-06-04 04:30:37,898 - root - INFO - Recreated application dropdown menu
2025-06-04 04:30:37,898 - root - INFO - Using already loaded reaction role data: message_id=1379581434154713148, channel_id=1367069308164902913
2025-06-04 04:30:37,898 - root - INFO - Reaction roles structure: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:30:37,898 - root - INFO - Found channel for reaction roles: test6
2025-06-04 04:30:38,309 - root - INFO - Found existing reaction message: 1379581434154713148
2025-06-04 04:30:38,958 - root - INFO - Reaction roles content: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:30:38,958 - root - INFO - Using loaded reaction_roles: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:30:38,959 - root - INFO - Using reaction roles: {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}
2025-06-04 04:30:38,959 - root - INFO - Processing role: emoji=[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife), role_id=1363215788227694863, type=<class 'bson.int64.Int64'>
2025-06-04 04:30:38,959 - root - WARNING - Invalid emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) for role 'level' 10, skipping
2025-06-04 04:30:39,358 - root - INFO - Recreated reaction role message
2025-06-04 04:30:39,358 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 04:30:39,358 - root - INFO - Bot initialization completed!
2025-06-04 04:48:26,039 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:48:26,039 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:48:26,039 - root - INFO - Initializing performance optimization systems...
2025-06-04 04:48:26,055 - root - INFO - Enhanced database connected successfully
2025-06-04 04:48:26,055 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 04:48:26,055 - root - INFO - Memory manager started
2025-06-04 04:48:26,055 - root - INFO - Loading ticket configuration...
2025-06-04 04:48:26,058 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 04:48:26,058 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 04:48:27,267 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379592728257630271
2025-06-04 04:48:27,267 - root - INFO - Loading bot data...
2025-06-04 04:48:27,269 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 04:48:27,270 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 04:48:27,270 - root - INFO - Falling back to regular database for data loading
2025-06-04 04:48:27,270 - root - INFO - Loading bot data from MongoDB...
2025-06-04 04:48:27,275 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 04:48:27,275 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 04:48:27,276 - root - INFO - Loaded 1 sticky messages
2025-06-04 04:48:27,276 - root - INFO - Loaded reaction roles: 1 roles, MessageID=1379581434154713148, ChannelID=1367069308164902913
2025-06-04 04:48:27,276 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 04:48:27,276 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 04:48:27,276 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 04:48:27,789 - root - INFO - Synced 35 command(s)!
2025-06-04 04:48:27,789 - root - INFO - Started vanity status check task
2025-06-04 04:48:27,789 - root - INFO - Restored application buttons
2025-06-04 04:48:27,789 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 04:48:27,789 - root - INFO - Found reaction role channel: test6
2025-06-04 04:48:28,203 - root - INFO - Found existing reaction role message: 1379581434154713148
2025-06-04 04:48:28,858 - root - INFO - Cleared existing reactions
2025-06-04 04:48:28,858 - root - INFO - Adding reaction role: [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) -> 'level' 10
2025-06-04 04:48:29,189 - root - ERROR - Error adding reaction for emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife): 404 Not Found (error code: 0): 404: Not Found
2025-06-04 04:48:29,577 - root - INFO - Reaction roles panel restored successfully
2025-06-04 04:48:29,577 - root - INFO - Restored reaction roles panel
2025-06-04 04:48:29,577 - root - INFO - No expired gang invitations found
2025-06-04 04:48:29,577 - root - INFO - Cleaned up expired gang invitations
2025-06-04 04:48:29,577 - root - INFO - Starting gang invitation view restoration...
2025-06-04 04:48:29,577 - root - INFO - Found 1 pending gang invitations
2025-06-04 04:48:29,577 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 04:48:29,577 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 04:48:29,577 - root - INFO - Restored gang invitation views
2025-06-04 04:48:31,192 - root - INFO - Recreated application dropdown menu
2025-06-04 04:48:31,192 - root - INFO - Using already loaded reaction role data: message_id=1379581434154713148, channel_id=1367069308164902913
2025-06-04 04:48:31,192 - root - INFO - Reaction roles structure: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:48:31,192 - root - INFO - Found channel for reaction roles: test6
2025-06-04 04:48:31,558 - root - INFO - Found existing reaction message: 1379581434154713148
2025-06-04 04:48:32,162 - root - INFO - Reaction roles content: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:48:32,162 - root - INFO - Using loaded reaction_roles: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:48:32,162 - root - INFO - Using reaction roles: {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}
2025-06-04 04:48:32,162 - root - INFO - Processing role: emoji=[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife), role_id=1363215788227694863, type=<class 'bson.int64.Int64'>
2025-06-04 04:48:32,162 - root - WARNING - Invalid emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) for role 'level' 10, skipping
2025-06-04 04:48:32,638 - root - INFO - Recreated reaction role message
2025-06-04 04:48:32,638 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 04:48:32,639 - root - INFO - Bot initialization completed!
2025-06-04 04:51:49,617 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:51:49,617 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 04:51:49,617 - root - INFO - Initializing performance optimization systems...
2025-06-04 04:51:49,633 - root - INFO - Enhanced database connected successfully
2025-06-04 04:51:49,633 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 04:51:49,634 - root - INFO - Memory manager started
2025-06-04 04:51:49,634 - root - INFO - Loading ticket configuration...
2025-06-04 04:51:49,636 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 04:51:49,637 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 04:51:51,022 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379593582197080165
2025-06-04 04:51:51,023 - root - INFO - Loading bot data...
2025-06-04 04:51:51,024 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 04:51:51,026 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 04:51:51,026 - root - INFO - Falling back to regular database for data loading
2025-06-04 04:51:51,026 - root - INFO - Loading bot data from MongoDB...
2025-06-04 04:51:51,033 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 04:51:51,033 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 04:51:51,034 - root - INFO - Loaded 1 sticky messages
2025-06-04 04:51:51,034 - root - INFO - Loaded reaction roles: 1 roles, MessageID=1379581434154713148, ChannelID=1367069308164902913
2025-06-04 04:51:51,034 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 04:51:51,034 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 04:51:51,034 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 04:51:51,517 - root - INFO - Synced 34 command(s)!
2025-06-04 04:51:51,517 - root - INFO - Started vanity status check task
2025-06-04 04:51:51,517 - root - INFO - Restored application buttons
2025-06-04 04:51:51,517 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 04:51:51,517 - root - INFO - Found reaction role channel: test6
2025-06-04 04:51:51,899 - root - INFO - Found existing reaction role message: 1379581434154713148
2025-06-04 04:51:52,592 - root - INFO - Cleared existing reactions
2025-06-04 04:51:52,592 - root - INFO - Adding reaction role: [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) -> 'level' 10
2025-06-04 04:51:52,931 - root - ERROR - Error adding reaction for emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife): 404 Not Found (error code: 0): 404: Not Found
2025-06-04 04:51:53,400 - root - INFO - Reaction roles panel restored successfully
2025-06-04 04:51:53,400 - root - INFO - Restored reaction roles panel
2025-06-04 04:51:53,400 - root - INFO - No expired gang invitations found
2025-06-04 04:51:53,400 - root - INFO - Cleaned up expired gang invitations
2025-06-04 04:51:53,400 - root - INFO - Starting gang invitation view restoration...
2025-06-04 04:51:53,400 - root - INFO - Found 1 pending gang invitations
2025-06-04 04:51:53,400 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 04:51:53,400 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 04:51:53,400 - root - INFO - Restored gang invitation views
2025-06-04 04:51:54,727 - root - INFO - Recreated application dropdown menu
2025-06-04 04:51:54,727 - root - INFO - Using already loaded reaction role data: message_id=1379581434154713148, channel_id=1367069308164902913
2025-06-04 04:51:54,727 - root - INFO - Reaction roles structure: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:51:54,727 - root - INFO - Found channel for reaction roles: test6
2025-06-04 04:51:55,119 - root - INFO - Found existing reaction message: 1379581434154713148
2025-06-04 04:51:55,712 - root - INFO - Reaction roles content: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:51:55,712 - root - INFO - Using loaded reaction_roles: {'roles': {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}, 'config': {'allow_multiple': False}}
2025-06-04 04:51:55,712 - root - INFO - Using reaction roles: {'[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife)': 1363215788227694863}
2025-06-04 04:51:55,712 - root - INFO - Processing role: emoji=[vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife), role_id=1363215788227694863, type=<class 'bson.int64.Int64'>
2025-06-04 04:51:55,712 - root - WARNING - Invalid emoji [vlife](https://cdn.discordapp.com/emojis/1187415430923567276.gif?size=48&animated=true&name=vlife) for role 'level' 10, skipping
2025-06-04 04:51:56,933 - root - INFO - Recreated reaction role message
2025-06-04 04:51:56,933 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 04:51:56,934 - root - INFO - Bot initialization completed!
2025-06-04 05:26:21,473 - root - INFO - Permission Check - Command: setup_reaction_roles
2025-06-04 05:26:21,473 - root - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-06-04 05:26:21,473 - root - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064)
2025-06-04 05:26:21,473 - root - INFO - Has Admin Permission: True
2025-06-04 05:26:22,755 - root - INFO - Created new reaction roles message with ID: 1379602270689296489 in channel: 1367069308164902913
2025-06-04 05:26:22,755 - root - INFO - Saving reaction roles data...
2025-06-04 05:26:22,802 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 05:26:29,105 - root - INFO - Adding reaction role: emoji=✅, role='level' 14 (ID: 1363215797354234010)
2025-06-04 05:26:29,106 - root - INFO - Saving reaction roles data...
2025-06-04 05:26:29,110 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 05:26:35,601 - root - INFO - Adding reaction role: emoji=❌, role='level' 13 (ID: 1363215795223793675)
2025-06-04 05:26:35,602 - root - INFO - Saving reaction roles data...
2025-06-04 05:26:35,607 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 05:26:44,282 - root - INFO - Adding reaction role: emoji=❤️‍🔥, role='level' 12 (ID: 1363215791956164992)
2025-06-04 05:26:44,282 - root - INFO - Saving reaction roles data...
2025-06-04 05:26:44,287 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 05:26:50,990 - root - INFO - Adding reaction role: emoji=[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1), role='level' 11 (ID: 1363215790186168320)
2025-06-04 05:26:50,990 - root - INFO - Saving reaction roles data...
2025-06-04 05:26:50,995 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 05:27:09,780 - root - INFO - Saving reaction roles data...
2025-06-04 05:27:09,785 - root - INFO - Reaction roles data saved successfully to MongoDB
2025-06-04 05:30:51,576 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 05:30:51,576 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 05:30:51,576 - root - INFO - Initializing performance optimization systems...
2025-06-04 05:30:51,592 - root - INFO - Enhanced database connected successfully
2025-06-04 05:30:51,592 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 05:30:51,592 - root - INFO - Memory manager started
2025-06-04 05:30:51,592 - root - INFO - Loading ticket configuration...
2025-06-04 05:30:51,595 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 05:30:51,595 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 05:30:53,492 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379603406775390301
2025-06-04 05:30:53,492 - root - INFO - Loading bot data...
2025-06-04 05:30:53,493 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 05:30:53,494 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 05:30:53,494 - root - INFO - Falling back to regular database for data loading
2025-06-04 05:30:53,494 - root - INFO - Loading bot data from MongoDB...
2025-06-04 05:30:53,500 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 05:30:53,500 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 05:30:53,500 - root - INFO - Loaded 1 sticky messages
2025-06-04 05:30:53,500 - root - INFO - Loaded reaction roles: 4 roles, MessageID=1379602270689296489, ChannelID=1367069308164902913
2025-06-04 05:30:53,500 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 05:30:53,500 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 05:30:53,501 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 05:30:54,179 - root - INFO - Synced 34 command(s)!
2025-06-04 05:30:54,179 - root - INFO - Started vanity status check task
2025-06-04 05:30:54,179 - root - INFO - Restored application buttons
2025-06-04 05:30:54,179 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 05:30:54,179 - root - INFO - Found reaction role channel: test6
2025-06-04 05:30:54,850 - root - INFO - Found existing reaction role message: 1379602270689296489
2025-06-04 05:30:55,578 - root - INFO - Cleared existing reactions
2025-06-04 05:30:55,578 - root - INFO - Adding reaction role: ✅ -> 'level' 14
2025-06-04 05:30:57,193 - root - INFO - Adding reaction role: ❌ -> 'level' 13
2025-06-04 05:30:57,885 - root - INFO - Adding reaction role: ❤️‍🔥 -> 'level' 12
2025-06-04 05:30:58,717 - root - INFO - Adding reaction role: [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) -> 'level' 11
2025-06-04 05:30:59,118 - root - ERROR - Error adding reaction for emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1): 404 Not Found (error code: 0): 404: Not Found
2025-06-04 05:30:59,560 - root - INFO - Reaction roles panel restored successfully
2025-06-04 05:30:59,560 - root - INFO - Restored reaction roles panel
2025-06-04 05:30:59,560 - root - INFO - No expired gang invitations found
2025-06-04 05:30:59,560 - root - INFO - Cleaned up expired gang invitations
2025-06-04 05:30:59,560 - root - INFO - Starting gang invitation view restoration...
2025-06-04 05:30:59,560 - root - INFO - Found 1 pending gang invitations
2025-06-04 05:30:59,560 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 05:30:59,560 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 05:30:59,560 - root - INFO - Restored gang invitation views
2025-06-04 05:31:01,515 - root - INFO - Recreated application dropdown menu
2025-06-04 05:31:01,515 - root - INFO - Using already loaded reaction role data: message_id=1379602270689296489, channel_id=1367069308164902913
2025-06-04 05:31:01,515 - root - INFO - Reaction roles structure: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 05:31:01,515 - root - INFO - Found channel for reaction roles: test6
2025-06-04 05:31:02,109 - root - INFO - Found existing reaction message: 1379602270689296489
2025-06-04 05:31:02,792 - root - INFO - Reaction roles content: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 05:31:02,792 - root - INFO - Using loaded reaction_roles: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 05:31:02,792 - root - INFO - Using reaction roles: {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}
2025-06-04 05:31:02,792 - root - INFO - Processing role: emoji=✅, role_id=1363215797354234010, type=<class 'bson.int64.Int64'>
2025-06-04 05:31:04,597 - root - INFO - Found role: 'level' 14
2025-06-04 05:31:04,598 - root - INFO - Processing role: emoji=❌, role_id=1363215795223793675, type=<class 'bson.int64.Int64'>
2025-06-04 05:31:06,119 - root - INFO - Found role: 'level' 13
2025-06-04 05:31:06,119 - root - INFO - Processing role: emoji=❤️‍🔥, role_id=1363215791956164992, type=<class 'bson.int64.Int64'>
2025-06-04 05:31:07,831 - root - INFO - Found role: 'level' 12
2025-06-04 05:31:07,831 - root - INFO - Processing role: emoji=[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1), role_id=1363215790186168320, type=<class 'bson.int64.Int64'>
2025-06-04 05:31:07,832 - root - WARNING - Rejected invalid emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Invalid emoji format '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)'
2025-06-04 05:31:08,289 - root - INFO - Recreated reaction role message
2025-06-04 05:31:08,289 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 05:31:08,289 - root - INFO - Bot initialization completed!
2025-06-04 15:12:58,947 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 15:12:58,948 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 15:12:58,948 - root - INFO - Initializing performance optimization systems...
2025-06-04 15:13:04,035 - enhanced_database - ERROR - Failed to connect to MongoDB: 
2025-06-04 15:13:04,036 - root - INFO - Bot Status: Performance optimization initialization failed: Failed to connect to database
2025-06-04 15:13:04,036 - root - WARNING - Performance optimization initialization failed: Failed to connect to database
2025-06-04 15:13:04,036 - root - INFO - Memory manager started
2025-06-04 15:13:04,036 - root - INFO - Loading ticket configuration...
2025-06-04 15:13:34,396 - root - WARNING - Failed to load ticket configuration
2025-06-04 15:13:34,397 - root - INFO - Loading bot data...
2025-06-04 15:13:34,398 - enhanced_database - WARNING - Collection settings not found
2025-06-04 15:13:34,399 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 15:13:34,399 - root - INFO - Falling back to regular database for data loading
2025-06-04 15:13:34,399 - root - INFO - Loading bot data from MongoDB...
2025-06-04 15:13:47,676 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "D:\MissMinutesBotmain\bot.py", line 6911, in <module>
    bot.run(token)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\discord\client.py", line 906, in run
    asyncio.run(runner())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "D:\MissMinutesBotmain\bot.py", line 5715, in on_ready
    await optimized_load_data()
  File "D:\MissMinutesBotmain\bot.py", line 187, in optimized_load_data
    return await load_data()
  File "D:\MissMinutesBotmain\bot.py", line 5490, in load_data
    gangs_data = db["gangs"].find_one({"_id": "gangs"})
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\collection.py", line 1755, in find_one
    for result in cursor.limit(-1):
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1284, in __next__
    return self.next()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1260, in next
    if len(self._data) or self._refresh():
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1208, in _refresh
    self._send_message(q)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1102, in _send_message
    response = client._run_operation(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\_csot.py", line 119, in csot_wrapper
    return func(self, *args, **kwargs)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1917, in _run_operation
    return self._retryable_read(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2026, in _retryable_read
    return self._retry_internal(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\_csot.py", line 119, in csot_wrapper
    return func(self, *args, **kwargs)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2875, in _read
    self._server = self._get_server()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2823, in _get_server
    return self._client._select_server(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1812, in _select_server
    server = topology.select_server(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 409, in select_server
    server = self._select_server(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 387, in _select_server
    servers = self.select_servers(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 294, in select_servers
    server_descriptions = self._select_servers_loop(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 368, in _select_servers_loop
    _cond_wait(self._condition, common.MIN_HEARTBEAT_INTERVAL)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\lock.py", line 92, in _cond_wait
    return condition.wait(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 363, in wait
    gotit = waiter.acquire(True, timeout)

2025-06-04 15:13:57,691 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "D:\MissMinutesBotmain\bot.py", line 6911, in <module>
    bot.run(token)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\discord\client.py", line 906, in run
    asyncio.run(runner())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\discord\client.py", line 481, in _run_event
    await coro(*args, **kwargs)
  File "D:\MissMinutesBotmain\bot.py", line 5715, in on_ready
    await optimized_load_data()
  File "D:\MissMinutesBotmain\bot.py", line 187, in optimized_load_data
    return await load_data()
  File "D:\MissMinutesBotmain\bot.py", line 5490, in load_data
    gangs_data = db["gangs"].find_one({"_id": "gangs"})
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\collection.py", line 1755, in find_one
    for result in cursor.limit(-1):
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1284, in __next__
    return self.next()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1260, in next
    if len(self._data) or self._refresh():
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1208, in _refresh
    self._send_message(q)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1102, in _send_message
    response = client._run_operation(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\_csot.py", line 119, in csot_wrapper
    return func(self, *args, **kwargs)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1917, in _run_operation
    return self._retryable_read(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2026, in _retryable_read
    return self._retry_internal(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\_csot.py", line 119, in csot_wrapper
    return func(self, *args, **kwargs)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2875, in _read
    self._server = self._get_server()
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2823, in _get_server
    return self._client._select_server(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1812, in _select_server
    server = topology.select_server(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 409, in select_server
    server = self._select_server(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 387, in _select_server
    servers = self.select_servers(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 294, in select_servers
    server_descriptions = self._select_servers_loop(
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 368, in _select_servers_loop
    _cond_wait(self._condition, common.MIN_HEARTBEAT_INTERVAL)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\lock.py", line 92, in _cond_wait
    return condition.wait(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 363, in wait
    gotit = waiter.acquire(True, timeout)

2025-06-04 15:14:04,451 - root - ERROR - Error in data loading: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 68400e3e5c56dd093ea8ee98, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
Traceback (most recent call last):
  File "D:\MissMinutesBotmain\bot.py", line 5490, in load_data
    gangs_data = db["gangs"].find_one({"_id": "gangs"})
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\collection.py", line 1755, in find_one
    for result in cursor.limit(-1):
                  ~~~~~~~~~~~~^^^^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1284, in __next__
    return self.next()
           ~~~~~~~~~^^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1260, in next
    if len(self._data) or self._refresh():
                          ~~~~~~~~~~~~~^^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1208, in _refresh
    self._send_message(q)
    ~~~~~~~~~~~~~~~~~~^^^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\cursor.py", line 1102, in _send_message
    response = client._run_operation(
        operation, self._unpack_response, address=self._address
    )
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\_csot.py", line 119, in csot_wrapper
    return func(self, *args, **kwargs)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1917, in _run_operation
    return self._retryable_read(
           ~~~~~~~~~~~~~~~~~~~~^
        _cmd,
        ^^^^^
    ...<4 lines>...
        operation=operation.name,
        ^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2026, in _retryable_read
    return self._retry_internal(
           ~~~~~~~~~~~~~~~~~~~~^
        func,
        ^^^^^
    ...<7 lines>...
        operation_id=operation_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\_csot.py", line 119, in csot_wrapper
    return func(self, *args, **kwargs)
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1993, in _retry_internal
    ).run()
      ~~~^^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2730, in run
    return self._read() if self._is_read else self._write()
           ~~~~~~~~~~^^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2875, in _read
    self._server = self._get_server()
                   ~~~~~~~~~~~~~~~~^^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 2823, in _get_server
    return self._client._select_server(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self._server_selector,
        ^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
        operation_id=self._operation_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\mongo_client.py", line 1812, in _select_server
    server = topology.select_server(
        server_selector,
    ...<2 lines>...
        operation_id=operation_id,
    )
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 409, in select_server
    server = self._select_server(
        selector,
    ...<4 lines>...
        operation_id=operation_id,
    )
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 387, in _select_server
    servers = self.select_servers(
        selector, operation, server_selection_timeout, address, operation_id
    )
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 294, in select_servers
    server_descriptions = self._select_servers_loop(
        selector, server_timeout, operation, operation_id, address
    )
  File "D:\MissMinutesBotmain\.venv\Lib\site-packages\pymongo\synchronous\topology.py", line 344, in _select_servers_loop
    raise ServerSelectionTimeoutError(
        f"{self._error_message(selector)}, Timeout: {timeout}s, Topology Description: {self.description!r}"
    )
pymongo.errors.ServerSelectionTimeoutError: localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms), Timeout: 30s, Topology Description: <TopologyDescription id: 68400e3e5c56dd093ea8ee98, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None, error=AutoReconnect('localhost:27017: [WinError 10061] No connection could be made because the target machine actively refused it (configured timeouts: socketTimeoutMS: 20000.0ms, connectTimeoutMS: 20000.0ms)')>]>
2025-06-04 15:14:04,455 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 15:14:04,455 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 15:14:44,255 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 15:14:44,255 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 15:14:44,255 - root - INFO - Initializing performance optimization systems...
2025-06-04 15:14:44,291 - root - INFO - Enhanced database connected successfully
2025-06-04 15:14:44,291 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 15:14:44,291 - root - INFO - Memory manager started
2025-06-04 15:14:44,291 - root - INFO - Loading ticket configuration...
2025-06-04 15:14:44,346 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 15:14:44,347 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 15:14:46,079 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379750351229681745
2025-06-04 15:14:46,080 - root - INFO - Loading bot data...
2025-06-04 15:14:46,084 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 15:14:46,097 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 15:14:46,097 - root - INFO - Falling back to regular database for data loading
2025-06-04 15:14:46,097 - root - INFO - Loading bot data from MongoDB...
2025-06-04 15:14:46,358 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 15:14:46,358 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 15:14:46,358 - root - INFO - Loaded 1 sticky messages
2025-06-04 15:14:46,358 - root - INFO - Loaded reaction roles: 4 roles, MessageID=1379602270689296489, ChannelID=1367069308164902913
2025-06-04 15:14:46,358 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 15:14:46,358 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 15:14:46,358 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 15:14:47,077 - root - INFO - Synced 34 command(s)!
2025-06-04 15:14:47,077 - root - INFO - Started vanity status check task
2025-06-04 15:14:47,077 - root - INFO - Restored application buttons
2025-06-04 15:14:47,077 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 15:14:47,077 - root - INFO - Found reaction role channel: test6
2025-06-04 15:14:47,688 - root - INFO - Found existing reaction role message: 1379602270689296489
2025-06-04 15:14:48,398 - root - INFO - Cleared existing reactions
2025-06-04 15:14:48,399 - root - INFO - Adding reaction role: ✅ -> 'level' 14
2025-06-04 15:14:49,154 - root - INFO - Adding reaction role: ❌ -> 'level' 13
2025-06-04 15:14:49,997 - root - INFO - Adding reaction role: ❤️‍🔥 -> 'level' 12
2025-06-04 15:14:50,736 - root - INFO - Adding reaction role: [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) -> 'level' 11
2025-06-04 15:14:51,164 - root - ERROR - Error adding reaction for emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1): 404 Not Found (error code: 0): 404: Not Found
2025-06-04 15:14:51,588 - root - INFO - Reaction roles panel restored successfully
2025-06-04 15:14:51,588 - root - INFO - Restored reaction roles panel
2025-06-04 15:14:51,588 - root - INFO - No expired gang invitations found
2025-06-04 15:14:51,588 - root - INFO - Cleaned up expired gang invitations
2025-06-04 15:14:51,588 - root - INFO - Starting gang invitation view restoration...
2025-06-04 15:14:51,588 - root - INFO - Found 1 pending gang invitations
2025-06-04 15:14:51,589 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 15:14:51,589 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 15:14:51,589 - root - INFO - Restored gang invitation views
2025-06-04 15:14:53,746 - root - INFO - Recreated application dropdown menu
2025-06-04 15:14:53,774 - root - INFO - Using already loaded reaction role data: message_id=1379602270689296489, channel_id=1367069308164902913
2025-06-04 15:14:53,774 - root - INFO - Reaction roles structure: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 15:14:53,774 - root - INFO - Found channel for reaction roles: test6
2025-06-04 15:14:54,365 - root - INFO - Found existing reaction message: 1379602270689296489
2025-06-04 15:14:55,116 - root - INFO - Reaction roles content: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 15:14:55,116 - root - INFO - Using loaded reaction_roles: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 15:14:55,116 - root - INFO - Using reaction roles: {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}
2025-06-04 15:14:55,116 - root - INFO - Processing role: emoji=✅, role_id=1363215797354234010, type=<class 'bson.int64.Int64'>
2025-06-04 15:14:56,552 - root - INFO - Found role: 'level' 14
2025-06-04 15:14:56,552 - root - INFO - Processing role: emoji=❌, role_id=1363215795223793675, type=<class 'bson.int64.Int64'>
2025-06-04 15:14:58,123 - root - INFO - Found role: 'level' 13
2025-06-04 15:14:58,124 - root - INFO - Processing role: emoji=❤️‍🔥, role_id=1363215791956164992, type=<class 'bson.int64.Int64'>
2025-06-04 15:14:59,640 - root - INFO - Found role: 'level' 12
2025-06-04 15:14:59,640 - root - INFO - Processing role: emoji=[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1), role_id=1363215790186168320, type=<class 'bson.int64.Int64'>
2025-06-04 15:14:59,640 - root - ERROR - Error validating emoji <:vRDRLogo1:1359963082197172354>: name 'discord' is not defined
2025-06-04 15:14:59,640 - root - WARNING - Skipping emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Error validating emoji: name 'discord' is not defined
2025-06-04 15:15:00,171 - root - INFO - Recreated reaction role message
2025-06-04 15:15:00,171 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 15:15:00,171 - root - INFO - Bot initialization completed!
2025-06-04 15:30:06,090 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 15:30:06,132 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-04 15:30:06,132 - root - INFO - Initializing performance optimization systems...
2025-06-04 15:30:06,549 - root - INFO - Enhanced database connected successfully
2025-06-04 15:30:06,550 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-04 15:30:06,550 - root - INFO - Memory manager started
2025-06-04 15:30:06,550 - root - INFO - Loading ticket configuration...
2025-06-04 15:30:06,555 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-04 15:30:06,556 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-04 15:30:08,589 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379754220408016947
2025-06-04 15:30:08,590 - root - INFO - Loading bot data...
2025-06-04 15:30:08,593 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-04 15:30:08,627 - root - INFO - No data found in enhanced database, will use regular database
2025-06-04 15:30:08,627 - root - INFO - Falling back to regular database for data loading
2025-06-04 15:30:08,627 - root - INFO - Loading bot data from MongoDB...
2025-06-04 15:30:08,635 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-04 15:30:08,635 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-04 15:30:08,635 - root - INFO - Loaded 1 sticky messages
2025-06-04 15:30:08,635 - root - INFO - Loaded reaction roles: 4 roles, MessageID=1379602270689296489, ChannelID=1367069308164902913
2025-06-04 15:30:08,635 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-04 15:30:08,635 - root - INFO - Bot data loaded successfully (optimized)
2025-06-04 15:30:08,635 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-04 15:30:09,471 - root - INFO - Synced 34 command(s)!
2025-06-04 15:30:09,471 - root - INFO - Started vanity status check task
2025-06-04 15:30:09,471 - root - INFO - Restored application buttons
2025-06-04 15:30:09,471 - root - INFO - Starting reaction roles panel restoration...
2025-06-04 15:30:09,471 - root - INFO - Found reaction role channel: test6
2025-06-04 15:30:10,080 - root - INFO - Found existing reaction role message: 1379602270689296489
2025-06-04 15:30:11,294 - root - INFO - Cleared existing reactions
2025-06-04 15:30:12,158 - root - INFO - Adding reaction role: ✅ -> 'level' 14
2025-06-04 15:30:12,924 - root - INFO - Adding reaction role: ❌ -> 'level' 13
2025-06-04 15:30:14,290 - root - INFO - Adding reaction role: ❤️‍🔥 -> 'level' 12
2025-06-04 15:30:14,301 - root - WARNING - Skipped emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Invalid emoji format: Custom static emoji 'vRDRLogo1' (ID: 1359963082197172354) not found in server 'CRIMSON GAMING'
2025-06-04 15:30:15,074 - root - INFO - Reaction roles panel restored successfully
2025-06-04 15:30:15,074 - root - INFO - Restored reaction roles panel
2025-06-04 15:30:15,074 - root - INFO - No expired gang invitations found
2025-06-04 15:30:15,074 - root - INFO - Cleaned up expired gang invitations
2025-06-04 15:30:15,074 - root - INFO - Starting gang invitation view restoration...
2025-06-04 15:30:15,074 - root - INFO - Found 1 pending gang invitations
2025-06-04 15:30:15,074 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-04 15:30:15,074 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-04 15:30:15,075 - root - INFO - Restored gang invitation views
2025-06-04 15:30:18,983 - root - INFO - Recreated application dropdown menu
2025-06-04 15:30:18,983 - root - INFO - Using already loaded reaction role data: message_id=1379602270689296489, channel_id=1367069308164902913
2025-06-04 15:30:18,983 - root - INFO - Reaction roles structure: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 15:30:18,984 - root - INFO - Found channel for reaction roles: test6
2025-06-04 15:30:19,740 - root - INFO - Found existing reaction message: 1379602270689296489
2025-06-04 15:30:20,628 - root - INFO - Reaction roles content: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 15:30:20,628 - root - INFO - Using loaded reaction_roles: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-04 15:30:20,674 - root - INFO - Using reaction roles: {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}
2025-06-04 15:30:20,674 - root - INFO - Processing role: emoji=✅, role_id=1363215797354234010, type=<class 'bson.int64.Int64'>
2025-06-04 15:30:21,462 - root - INFO - Found role: 'level' 14
2025-06-04 15:30:21,462 - root - INFO - Processing role: emoji=❌, role_id=1363215795223793675, type=<class 'bson.int64.Int64'>
2025-06-04 15:30:22,287 - root - INFO - Found role: 'level' 13
2025-06-04 15:30:22,287 - root - INFO - Processing role: emoji=❤️‍🔥, role_id=1363215791956164992, type=<class 'bson.int64.Int64'>
2025-06-04 15:30:23,324 - root - INFO - Found role: 'level' 12
2025-06-04 15:30:23,324 - root - INFO - Processing role: emoji=[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1), role_id=1363215790186168320, type=<class 'bson.int64.Int64'>
2025-06-04 15:30:23,325 - root - WARNING - Skipping emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Invalid emoji format: Custom static emoji 'vRDRLogo1' (ID: 1359963082197172354) not found in server 'CRIMSON GAMING'
2025-06-04 15:30:23,995 - root - INFO - Recreated reaction role message
2025-06-04 15:30:23,995 - root - INFO - Bot Status: Bot initialization completed!
2025-06-04 15:30:23,995 - root - INFO - Bot initialization completed!
2025-06-05 04:26:10,725 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-05 04:26:10,732 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-05 04:26:10,732 - root - INFO - Initializing performance optimization systems...
2025-06-05 04:26:15,835 - enhanced_database - ERROR - Failed to connect to MongoDB: 
2025-06-05 04:26:15,835 - root - INFO - Bot Status: Performance optimization initialization failed: Failed to connect to database
2025-06-05 04:26:15,835 - root - WARNING - Performance optimization initialization failed: Failed to connect to database
2025-06-05 04:26:15,835 - root - INFO - Memory manager started
2025-06-05 04:26:15,835 - root - INFO - Loading ticket configuration...
2025-06-05 04:26:57,447 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-05 04:26:57,447 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-05 04:26:57,447 - root - INFO - Initializing performance optimization systems...
2025-06-05 04:26:57,464 - root - INFO - Enhanced database connected successfully
2025-06-05 04:26:57,464 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-05 04:26:57,464 - root - INFO - Memory manager started
2025-06-05 04:26:57,464 - root - INFO - Loading ticket configuration...
2025-06-05 04:26:57,539 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-05 04:26:57,539 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-05 04:26:59,232 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379949725440020530
2025-06-05 04:26:59,232 - root - INFO - Loading bot data...
2025-06-05 04:26:59,233 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-05 04:26:59,243 - root - INFO - No data found in enhanced database, will use regular database
2025-06-05 04:26:59,243 - root - INFO - Falling back to regular database for data loading
2025-06-05 04:26:59,243 - root - INFO - Loading bot data from MongoDB...
2025-06-05 04:26:59,452 - root - INFO - Loaded gang data: 1 gangs, 2 leaders, 1 invitations
2025-06-05 04:26:59,452 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-05 04:26:59,452 - root - INFO - Loaded 1 sticky messages
2025-06-05 04:26:59,452 - root - INFO - Loaded reaction roles: 4 roles, MessageID=1379602270689296489, ChannelID=1367069308164902913
2025-06-05 04:26:59,452 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-05 04:26:59,452 - root - INFO - Bot data loaded successfully (optimized)
2025-06-05 04:26:59,452 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-05 04:27:00,098 - root - INFO - Synced 34 command(s)!
2025-06-05 04:27:00,098 - root - INFO - Started vanity status check task
2025-06-05 04:27:00,098 - root - INFO - Restored application buttons
2025-06-05 04:27:00,098 - root - INFO - Starting reaction roles panel restoration...
2025-06-05 04:27:00,098 - root - INFO - Found reaction role channel: test6
2025-06-05 04:27:00,696 - root - INFO - Found existing reaction role message: 1379602270689296489
2025-06-05 04:27:01,359 - root - INFO - Cleared existing reactions
2025-06-05 04:27:02,068 - root - INFO - Adding reaction role: ✅ -> 'level' 14
2025-06-05 04:27:02,780 - root - INFO - Adding reaction role: ❌ -> 'level' 13
2025-06-05 04:27:03,499 - root - INFO - Adding reaction role: ❤️‍🔥 -> 'level' 12
2025-06-05 04:27:03,500 - root - WARNING - Skipped emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Invalid emoji format: Custom static emoji 'vRDRLogo1' (ID: 1359963082197172354) not found in server 'CRIMSON GAMING'
2025-06-05 04:27:03,959 - root - INFO - Reaction roles panel restored successfully
2025-06-05 04:27:03,959 - root - INFO - Restored reaction roles panel
2025-06-05 04:27:03,959 - root - INFO - No expired gang invitations found
2025-06-05 04:27:03,959 - root - INFO - Cleaned up expired gang invitations
2025-06-05 04:27:03,959 - root - INFO - Starting gang invitation view restoration...
2025-06-05 04:27:03,959 - root - INFO - Found 1 pending gang invitations
2025-06-05 04:27:03,959 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-05 04:27:03,959 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-05 04:27:03,959 - root - INFO - Restored gang invitation views
2025-06-05 04:27:05,703 - root - INFO - Recreated application dropdown menu
2025-06-05 04:27:05,704 - root - INFO - Using already loaded reaction role data: message_id=1379602270689296489, channel_id=1367069308164902913
2025-06-05 04:27:05,704 - root - INFO - Reaction roles structure: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-05 04:27:05,704 - root - INFO - Found channel for reaction roles: test6
2025-06-05 04:27:06,259 - root - INFO - Found existing reaction message: 1379602270689296489
2025-06-05 04:27:06,928 - root - INFO - Reaction roles content: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-05 04:27:06,928 - root - INFO - Using loaded reaction_roles: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-05 04:27:06,928 - root - INFO - Using reaction roles: {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}
2025-06-05 04:27:06,928 - root - INFO - Processing role: emoji=✅, role_id=1363215797354234010, type=<class 'bson.int64.Int64'>
2025-06-05 04:27:07,758 - root - INFO - Found role: 'level' 14
2025-06-05 04:27:07,758 - root - INFO - Processing role: emoji=❌, role_id=1363215795223793675, type=<class 'bson.int64.Int64'>
2025-06-05 04:27:08,454 - root - INFO - Found role: 'level' 13
2025-06-05 04:27:08,454 - root - INFO - Processing role: emoji=❤️‍🔥, role_id=1363215791956164992, type=<class 'bson.int64.Int64'>
2025-06-05 04:27:09,230 - root - INFO - Found role: 'level' 12
2025-06-05 04:27:09,231 - root - INFO - Processing role: emoji=[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1), role_id=1363215790186168320, type=<class 'bson.int64.Int64'>
2025-06-05 04:27:09,231 - root - WARNING - Skipping emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Invalid emoji format: Custom static emoji 'vRDRLogo1' (ID: 1359963082197172354) not found in server 'CRIMSON GAMING'
2025-06-05 04:27:09,668 - root - INFO - Recreated reaction role message
2025-06-05 04:27:09,668 - root - INFO - Bot Status: Bot initialization completed!
2025-06-05 04:27:09,668 - root - INFO - Bot initialization completed!
2025-06-05 04:28:38,017 - root - INFO - Command executed: create_gang (Red Mafia)
2025-06-05 04:28:38,017 - root - INFO - User: crimsonop69#0 (ID: 567939816721874957)
2025-06-05 04:28:38,017 - root - INFO - Guild: CRIMSON GAMING (ID: 656611572021592064) if guild else 'DM'
2025-06-05 04:28:38,017 - root - INFO - Success: True
2025-06-05 04:28:38,018 - root - INFO - Creating gang 'Red Mafia' with leader Crimsonop (ID: 815074630171623425)
2025-06-05 04:28:38,543 - root - INFO - Successfully added role 'team-1' to Crimsonop
2025-06-05 04:28:38,544 - root - INFO - Gang 'Red Mafia' created successfully with 5 member limit
2025-06-05 04:28:38,546 - root - INFO - Data saved successfully to MongoDB
2025-06-05 04:28:54,169 - root - INFO - Data saved successfully to MongoDB
2025-06-05 04:29:26,043 - root - INFO - Bot Status: Logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-05 04:29:26,043 - root - INFO - Bot logged in as Miss Minutes#7930 (ID: 1309216318158929921)
2025-06-05 04:29:26,043 - root - INFO - Initializing performance optimization systems...
2025-06-05 04:29:26,071 - root - INFO - Enhanced database connected successfully
2025-06-05 04:29:26,071 - root - INFO - Bot Status: Performance optimization systems initialized
2025-06-05 04:29:26,071 - root - INFO - Memory manager started
2025-06-05 04:29:26,071 - root - INFO - Loading ticket configuration...
2025-06-05 04:29:26,075 - ticket_system - INFO - Clearing old ticket views to prevent interaction conflicts
2025-06-05 04:29:26,075 - ticket_system - INFO - Creating ticket panel in channel test3 (ID: 1367069302003335278)
2025-06-05 04:29:27,987 - ticket_system - INFO - Ticket panel created successfully with message ID: 1379950349145739304
2025-06-05 04:29:27,987 - root - INFO - Loading bot data...
2025-06-05 04:29:27,989 - ticket_system - INFO - Saved ticket configuration successfully to MongoDB
2025-06-05 04:29:27,990 - root - INFO - No data found in enhanced database, will use regular database
2025-06-05 04:29:27,990 - root - INFO - Falling back to regular database for data loading
2025-06-05 04:29:27,990 - root - INFO - Loading bot data from MongoDB...
2025-06-05 04:29:28,005 - root - INFO - Loaded gang data: 2 gangs, 3 leaders, 1 invitations
2025-06-05 04:29:28,006 - root - INFO - Loaded applications: 1 forms, 2 status entries
2025-06-05 04:29:28,006 - root - INFO - Loaded 1 sticky messages
2025-06-05 04:29:28,006 - root - INFO - Loaded reaction roles: 4 roles, MessageID=1379602270689296489, ChannelID=1367069308164902913
2025-06-05 04:29:28,006 - root - INFO - Bot data loaded successfully from MongoDB
2025-06-05 04:29:28,006 - root - INFO - Bot data loaded successfully (optimized)
2025-06-05 04:29:28,006 - root - INFO - Starting command sync process (attempt 1/3)...
2025-06-05 04:29:28,593 - root - INFO - Synced 37 command(s)!
2025-06-05 04:29:28,593 - root - INFO - Started vanity status check task
2025-06-05 04:29:28,593 - root - INFO - Restored application buttons
2025-06-05 04:29:28,593 - root - INFO - Starting reaction roles panel restoration...
2025-06-05 04:29:28,593 - root - INFO - Found reaction role channel: test6
2025-06-05 04:29:29,214 - root - INFO - Found existing reaction role message: 1379602270689296489
2025-06-05 04:29:29,965 - root - INFO - Cleared existing reactions
2025-06-05 04:29:30,741 - root - INFO - Adding reaction role: ✅ -> 'level' 14
2025-06-05 04:29:31,465 - root - INFO - Adding reaction role: ❌ -> 'level' 13
2025-06-05 04:29:32,192 - root - INFO - Adding reaction role: ❤️‍🔥 -> 'level' 12
2025-06-05 04:29:32,192 - root - WARNING - Skipped emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Invalid emoji format: Custom static emoji 'vRDRLogo1' (ID: 1359963082197172354) not found in server 'CRIMSON GAMING'
2025-06-05 04:29:32,673 - root - INFO - Reaction roles panel restored successfully
2025-06-05 04:29:32,673 - root - INFO - Restored reaction roles panel
2025-06-05 04:29:32,673 - root - INFO - No expired gang invitations found
2025-06-05 04:29:32,673 - root - INFO - Cleaned up expired gang invitations
2025-06-05 04:29:32,673 - root - INFO - Starting gang invitation view restoration...
2025-06-05 04:29:32,673 - root - INFO - Found 1 pending gang invitations
2025-06-05 04:29:32,673 - root - INFO - Restored gang invitation view for invitation: RbiT_726537260891373639_1748769903 (Gang: RbiT)
2025-06-05 04:29:32,673 - root - INFO - Gang invitation view restoration completed: 1 restored, 0 failed
2025-06-05 04:29:32,673 - root - INFO - Restored gang invitation views
2025-06-05 04:29:34,457 - root - INFO - Recreated application dropdown menu
2025-06-05 04:29:34,457 - root - INFO - Using already loaded reaction role data: message_id=1379602270689296489, channel_id=1367069308164902913
2025-06-05 04:29:34,457 - root - INFO - Reaction roles structure: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-05 04:29:34,457 - root - INFO - Found channel for reaction roles: test6
2025-06-05 04:29:35,093 - root - INFO - Found existing reaction message: 1379602270689296489
2025-06-05 04:29:35,851 - root - INFO - Reaction roles content: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-05 04:29:35,852 - root - INFO - Using loaded reaction_roles: {'roles': {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}, 'config': {'allow_multiple': False}}
2025-06-05 04:29:35,852 - root - INFO - Using reaction roles: {'✅': 1363215797354234010, '❌': 1363215795223793675, '❤️\u200d🔥': 1363215791956164992, '[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1)': 1363215790186168320}
2025-06-05 04:29:35,852 - root - INFO - Processing role: emoji=✅, role_id=1363215797354234010, type=<class 'bson.int64.Int64'>
2025-06-05 04:29:36,596 - root - INFO - Found role: 'level' 14
2025-06-05 04:29:36,597 - root - INFO - Processing role: emoji=❌, role_id=1363215795223793675, type=<class 'bson.int64.Int64'>
2025-06-05 04:29:37,363 - root - INFO - Found role: 'level' 13
2025-06-05 04:29:37,363 - root - INFO - Processing role: emoji=❤️‍🔥, role_id=1363215791956164992, type=<class 'bson.int64.Int64'>
2025-06-05 04:29:38,055 - root - INFO - Found role: 'level' 12
2025-06-05 04:29:38,055 - root - INFO - Processing role: emoji=[vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1), role_id=1363215790186168320, type=<class 'bson.int64.Int64'>
2025-06-05 04:29:38,092 - root - WARNING - Skipping emoji [vRDRLogo1](https://cdn.discordapp.com/emojis/1359963082197172354.webp?size=48&name=vRDRLogo1) for role 'level' 11: Invalid emoji format: Custom static emoji 'vRDRLogo1' (ID: 1359963082197172354) not found in server 'CRIMSON GAMING'
2025-06-05 04:29:38,575 - root - INFO - Recreated reaction role message
2025-06-05 04:29:38,576 - root - INFO - Bot Status: Bot initialization completed!
2025-06-05 04:29:38,576 - root - INFO - Bot initialization completed!
